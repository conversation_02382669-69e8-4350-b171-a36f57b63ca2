<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerGetByContractCodeAction;

use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class GetListPartnerByContractCodeAction
{
  public function run(Request $request)
  {
    $contractCode = trim($request->json('data.contract_code', ''));

    $partners = CollectDebtPartner::query()
                                  ->where('contract_code', $contractCode);
    
    $paymentMethodCode = $request->json('data.filter.payment_method_code');
    if ( !empty($paymentMethodCode) ) {
      $partners = $partners->where('payment_method_code', $paymentMethodCode);
    }

    $partnerRequestTransactionId = $request->json('data.filter.partner_request_transaction_id');
    
    if ( !empty($partnerRequestTransactionId) ) {
      $partners = $partners->where(function ($query) use ($partnerRequestTransactionId) {
        return $query->where('partner_request_id', $partnerRequestTransactionId)
                     ->orWhere('partner_transaction_id', $partnerRequestTransactionId);
      });
    }

    $status = $request->json('data.filter.status');

    if ( !empty($status) ) {
      $partners = $partners->where('status', $status);
    }

    $paymentAccountId = $request->json('data.filter.payment_account_id');

    if ( !empty($paymentAccountId) ) {
      $partners = $partners->where('payment_account_id', $paymentAccountId);
    }

		$fromDate = $request->json('data.filter.from_date');
		if (!empty($fromDate)) {
			$fromDateTimestamp = Carbon::createFromFormat('d-m-Y', $fromDate)->startOfDay()->timestamp;
			$partners = $partners->where('time_created', '>=', $fromDateTimestamp);
		}

		$toDate = $request->json('data.filter.to_date');
		if (!empty($toDate)) {
			$toDateTimestamp = Carbon::createFromFormat('d-m-Y', $toDate)->endOfDay()->timestamp;
			$partners = $partners->where('time_created', '<=', $toDateTimestamp);
		}

		$isCongNoBaoMuon = $request->json('data.filter.is_cong_no_bao_muon', false);
		if (!empty($isCongNoBaoMuon)) {
			$partners = $partners->where('description', 'LIKE', 'TRICH_MUON%');
		}

    $partners = $partners->orderBy('id', 'DESC')
                        ->selectRaw("id, payment_account_id, payment_method_code, partner_transaction_id, partner_request_id, amount_receiver, amount_payment, time_created, time_approved, time_complated, time_created_request, created_by, approved_by, complated_by, created_request_by, status, description, 'VND' as currency")
                        ->simplePaginate(
													$request->json('data.limit', 10),
													['*'],
													'page',
													$request->json('data.page', 1)
                      	);
							
    return $partners;
  }
}
