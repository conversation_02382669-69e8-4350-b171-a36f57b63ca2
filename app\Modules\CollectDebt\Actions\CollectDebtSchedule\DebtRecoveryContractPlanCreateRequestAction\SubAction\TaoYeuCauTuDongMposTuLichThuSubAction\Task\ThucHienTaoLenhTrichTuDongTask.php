<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\TaoYeuCauTuDongMposTuLichThuSubAction\Task;

use Exception;
use Carbon\Carbon;
use App\Lib\Helper;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class ThucHienTaoLenhTrichTuDongTask
{
	public function run(array $taoYeuCauTrichParam=[], Collection $listLichThuTaoYeuCau): CollectDebtRequest
	{
		try {
			$collectDebtRequest = CollectDebtRequest::query()->forceCreate($taoYeuCauTrichParam);
			
			if (!$collectDebtRequest) {
				mylog(['loi khong the tao yc' => $collectDebtRequest]);
				throw new Exception('loi khong the tao yc');
			}

			$collectDebtRequest->partner_request_id = sprintf('NL%s%s', date('ymd'), $collectDebtRequest->id);
			$otherData = [];

			if ($this->__isCoTheKeoDaiTimeExpired($listLichThuTaoYeuCau)) {
				$collectDebtRequest->time_expired = Carbon::createFromTimestamp($collectDebtRequest->time_expired)
																									->next('Monday')
																									->setTime(7, 0)
																									->timestamp;
			}

      $collectDebtRequest->other_data = json_encode($otherData);
			
      $saveResult = $collectDebtRequest->save();

			if (!$saveResult) {
				mylog(['loi khong the tao partner_transaction_id yc' => $saveResult]);
				throw new Exception('loi khong the tao partner_transaction_id yc');
			}

			return $collectDebtRequest;
		} catch (\Throwable $th) {
			mylog(['Loi tao yc, catch' => Helper::traceError($th)]);
			throw $th;
		}
	}

	/**
	 * Neu lich thu co rundate la chu nhat:
	 * 	1. Kiem tra trong danh sach lich thu co lich nao la lich thu tat toan khong -> neu co la dc phep keo dai
	 */
	private function __isCoTheKeoDaiTimeExpired(Collection $listLichThuTaoYeuCau): bool {
		$isRunDateChuNhat = $listLichThuTaoYeuCau->contains(function (CollectDebtSchedule $plan) {
			return $plan->rundate_as_date->isSunday();
		});
		
		if (!$isRunDateChuNhat) {
			return false;
		}

		// co chua lich thu goc chinh - tat toan: Thi keo dai
		$isCoChuaLichThuGocTatToan = $listLichThuTaoYeuCau->contains(function (CollectDebtSchedule $plan) {
			return $plan->isLichThuGocChinh() && $plan->isLichTatToan();
		});

		if ($isCoChuaLichThuGocTatToan) {
			return true;
		}

		// co chua lich thu goc ky - thi dc keo dai
		$isCoChuaLichThuGocChinhKy = $listLichThuTaoYeuCau->contains(function (CollectDebtSchedule $plan) {
			return $plan->isLichThuGocChinh() && $plan->contract_type == CollectDebtEnum::SCHEDULE_LOAI_HD_KHOAN_UNG_CHU_KY;
		});

		if ($isCoChuaLichThuGocChinhKy) {
			return true;
		}

		return false;
	}
} // End class
