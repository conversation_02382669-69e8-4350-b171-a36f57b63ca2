# 📄 <PERSON><PERSON><PERSON> c<PERSON>o Deploy Tuning – <PERSON><PERSON> thống Thu Nợ

## 1. Thông tin chung
- **Tên dự án**: <PERSON><PERSON> thống Thu Nợ V4 (Debt Recovery)  
- **Môi trường**: Production  
- **G<PERSON>i đo<PERSON>n**: 2/5  
- **Thời gian triển khai**: 08/08/2025 – 20/08/2025  
- **<PERSON><PERSON><PERSON><PERSON> thực hiện**: []  

---

## 2. <PERSON><PERSON><PERSON> tiêu Tuning
### 2.1. B<PERSON><PERSON> cảnh
- <PERSON><PERSON><PERSON> jobs nghiệp vụ chạy chậm và xử lý đồng bộ --> gây chậm
- Jobs cũng truy vấn WHERE trên 4-5 fields --> index không được tối ưu và scan nhiều
- Một số api trả ra khi theo dõi trên portal admin.nextlend.vn có dấu hiệu loading chậm 

### 2.2. <PERSON><PERSON><PERSON> đích
- <PERSON><PERSON><PERSON> bỏ các truy vấn nặng, scan dư thừa nhiều rows không cần thiết.  
- Giảm áp lực lên DB chính trong giờ cao điểm.  
- <PERSON>factor code base, loại bỏ các cronjob cũ.  
- Tối ưu tốc độ xử lý batch (tạo lệnh trích, gửi lệnh trích, xử lý tiền về, ghi sổ, hạch toán, đẩy tổng hợp, tất toán, đồng bộ về hệ thống hợp đồng).  
- Sử dụng Redis để cache dữ liệu ít thay đổi.  
- Chia tải DB (write, read)để giảm áp lực cho các nghiệp vụ tương tác với DB 
- Persistance connection để re-use lại các connection chưa đóng thay vì tạo các connection mới
---

## 3. Các thay đổi đã triển khai

### 3.1. **SQL Tuning**

#### Bảng `debt_recovery_processing`
- Tạo bảng mới để lưu trạng thái hợp đồng đang có lệnh trích tự động.  
- Dùng `expired_at` để cutoff thay vì quét vào bảng request lớn.  
- Khi lệnh trích được ghi sổ, hệ thống sẽ xóa bản ghi ngay → bảng luôn gọn nhẹ.  

```sql
CREATE TABLE `debt_recovery_processing` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'id tự tăng',
  `contract_code` varchar(50) NOT NULL COMMENT 'mã hợp đồng',
  `partner_request_id` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'mã lệnh trích',
  `created_at` datetime DEFAULT NULL COMMENT 'thời điểm tạo',
  `updated_at` datetime DEFAULT NULL COMMENT 'thời điểm cập nhật',
  `expired_at` datetime DEFAULT NULL COMMENT 'thời điểm hết hạn của lệnh trích tự động',
  `checked_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `is_sent_request` tinyint(1) DEFAULT '0' COMMENT 'đã gửi lệnh sang đối tác: 0-Chưa gửi; 1-Đã gửi',
  PRIMARY KEY (`id`),
  UNIQUE KEY `contract_code` (`contract_code`) USING BTREE,
  KEY `partner_request_id` (`partner_request_id`) USING BTREE,
  KEY `expired_at` (`expired_at`) USING BTREE,
  KEY `check_request_idx` (`is_sent_request`,`checked_at`,`id`) USING BTREE,
  KEY `send_request_idx` (`is_sent_request`,`expired_at`,`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=734 DEFAULT CHARSET=utf8mb3 COMMENT='Thể hiện hợp đồng đang processing';
```

#### Bảng `debt_recovery_statistic_partner`
- Lưu số liệu thống kê theo kênh (VA, MPOS, IB_OFF, WALLET).  
- Giúp API chỉ cần select 1 row thay vì COUNT/SUM nặng trên bảng request.  

```sql
CREATE TABLE `debt_recovery_statistic_partner` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `contract_code` varchar(50) NOT NULL,
  `mpos_total_request` int unsigned DEFAULT '0',
  `mpos_total_receipt` double DEFAULT '0',
  `mpos_total_repayment` double DEFAULT '0',
  `mpos_total_refund` double DEFAULT '0',
  `mpos_total_money_have` double DEFAULT '0',
  `ib_total_request` int unsigned DEFAULT '0',
  `ib_total_receipt` double DEFAULT '0',
  `ib_total_repayment` double DEFAULT '0',
  `ib_total_refund` double DEFAULT '0',
  `ib_total_money_have` double DEFAULT '0',
  `va_total_request` int unsigned DEFAULT '0',
  `va_total_receipt` double DEFAULT '0',
  `va_total_repayment` double DEFAULT '0',
  `va_total_refund` double DEFAULT '0',
  `va_total_money_have` double DEFAULT '0',
  `wallet_total_request` int unsigned DEFAULT '0',
  `wallet_total_receipt` double DEFAULT '0',
  `wallet_total_repayment` double DEFAULT '0',
  `wallet_total_refund` double DEFAULT '0',
  `wallet_total_money_have` double DEFAULT '0',
  `total_request` int unsigned DEFAULT NULL,
  `status_sync` tinyint unsigned DEFAULT '0',
  `time_sync` int unsigned DEFAULT NULL,
  `currency` varchar(3) DEFAULT 'VND',
  PRIMARY KEY (`id`),
  KEY `contract_code` (`contract_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='Thống kê số lệnh, số tiền theo kênh';
```

#### Bảng `debt_recovery_summary`
- Thêm các cột phục vụ tối ưu:  
  - `is_fully_paid`: cờ đánh dấu hợp đồng đủ điều kiện tất toán (generated column).  

```sql

ALTER TABLE debt_recovery_summary 
  ADD COLUMN is_fully_paid TINYINT(1) 
    GENERATED ALWAYS AS (
      (total_amount_paid >= contract_amount) 
      AND (fee_overdue_paid + fee_overdue_reduction >= fee_overdue) 
      AND (fee_overdue_cycle_reduction + fee_overdue_cycle_paid >= fee_overdue_cycle)
    ) STORED;

CREATE INDEX status_contract_ft_is_fully_paid 
  ON lending_debt.debt_recovery_summary (status_contract, is_fully_paid);
```

### 3.2. **Batching & Concurrency**
- Sử dụng **Guzzle Pool** với `concurrency = 10`.  
- Tăng khả năng xử lý batch với số lượng hợp đồng lớn.  

---

### 3.3. **Caching Tuning**
- Cache cấu hình hệ thống trong Redis.  
- Cache danh sách hợp đồng đang bị dừng job (20 phút, auto refresh khi có thay đổi).  
- Cache template email.  
- Các API nhỏ lẻ chỉ select field cần thiết thay vì `SELECT *`.  

---

### 3.4. **Scheduler / Cronjob**
- Loại bỏ dần cơ chế cron chỉ chạy theo phút.  
- Sử dụng background service (BGS) có thể chạy theo đơn vị giây.  
- Các job được tối ưu:  
  - `CreateRequestAutoBatchAction`  
  - `EnableStatusDebtAction` (chạy 1 lần duy nhất)  
  - `SendRequestBatchAction`  
  - `CheckRequestPaymentBatchAction`  
  - `PartnerCheckBatchAction`  
  - `LedgerRecordBatchAction`  
  - `CutOffRequestBatchAction`  
  - `AccountingLedgerBatchAction`  
  - `AccountingSummaryBatchAction`  
  - `SettleContractBatchAction`  
  - `SyncContractBatchAction`  --> đồng bộ thẳng về hệ thống hợp đồng mà không cần hook vào bảng event

---

### 3.5. **Code-level Optimization**
- Sử dụng `chunkById()` để lấy dữ liệu theo batch.  
- Xử lý logic batching trực tiếp trong **Guzzle Pool client** thay vì queue phức tạp.  
- Loại bỏ các `COUNT()`/`SUM()` trên bảng lớn → dùng bảng thống kê riêng.  
- Khoảng 50% codebase đã được refactor.  

---

### 3.6. **Web Backend Optimization**
- Tối ưu các màn hình bị call nặng.  
- Rõ ràng hóa chức năng/màn hình để dễ theo dõi và thao tác.  

---

## 4. Kết quả & Đánh giá
- **SQL query**: giảm đáng kể thời gian truy vấn ở các job chính nhờ scan ít rows hơn.  
- **Batch xử lý**: cải thiện ~40% tốc độ do tận dụng concurrency.  
- **DB pressure**: giảm tải đáng kể cho DB.  
- **Redis**: tốc độ đọc/ghi nhanh và ổn định hơn.  
- **Cronjob**: giảm số lượt quét thừa, nhẹ hơn trên DB.  
- **Chờ QC** tham gia kiểm thử và đưa ra đánh giá chính thức.  

---

📌 **Người lập báo cáo**: []  
📌 **Ngày**: []  