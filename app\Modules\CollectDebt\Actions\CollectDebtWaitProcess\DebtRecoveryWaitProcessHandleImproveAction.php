<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtWaitProcess;

use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtWaitProcess;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCancelPaymentAction\DebtRecoveryRequestCancelPaymentAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\CheckRequestViaMposSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFinishCutOffTimeAction\SubAction\CreatePartnerKhongDongDoChuDongHuySubAction;

class DebtRecoveryWaitProcessHandleImproveAction
{
	private array $__processDaXuLy = [];

	private array $__processIdsExcept = [];

	public function initWaitProcess()
	{
		for ($i = 1; $i <= 15; $i++) {
			try {
				$result = $this->run();

				// Truy van ma he thong tra ra "EMPTY", thi break luon
				if ($result == 'EMPTY') {
					$this->__processDaXuLy[] = 'khong co thong tin process de xu ly';
					break;
				}
			} catch (\Throwable $th) {
				
				continue;
			} finally {
				usleep(300000);
			}
		}

		return $this->__processDaXuLy;
	}


	public function run()
	{
		$collectDebtWaitProcess = CollectDebtWaitProcess::query()
			->where('status', CollectDebtEnum::WAIT_PROCESS_STT_CHUA_XU_LY)
			->where('obj_type', 'debt_recovery_request')
			->where('action_code', 'CANCEL');

		if (!empty($this->__processIdsExcept)) {
			$collectDebtWaitProcess = $collectDebtWaitProcess->whereNotIn('id', $this->__processIdsExcept);
		}

		$collectDebtWaitProcess = $collectDebtWaitProcess->first();

		if (!$collectDebtWaitProcess) {
			return 'EMPTY';
		}

		mylog(['ban ghi process' => $collectDebtWaitProcess]);
		$this->__processIdsExcept[] = $collectDebtWaitProcess->id;

		// Update lên đang xử lý
		$updatedLenDangXuLy = CollectDebtWaitProcess::query()
			->where('id', $collectDebtWaitProcess->id)
			->where('status', CollectDebtEnum::WAIT_PROCESS_STT_CHUA_XU_LY)
			->update([
				'status' => CollectDebtEnum::WAIT_PROCESS_STT_DANG_XU_LY
			]);

		if (!$updatedLenDangXuLy) {
			mylog(['loi cap nhat len dang xu ly' => 'ok']);
			throw new Exception('loi cap nhat len dang xu ly');
		}

		$collectDebtWaitProcess->refresh();

		if ($collectDebtWaitProcess->status != CollectDebtEnum::WAIT_PROCESS_STT_DANG_XU_LY) {
			mylog(['Loi trang thai khong phai dang xu ly, trang thai moi nhat la' => $collectDebtWaitProcess->status]);
			throw new Exception('Loi trang thai khong phai dang xu ly, trang thai moi nhat la');
		}

		DB::beginTransaction();
		try {
			$collectDebtRequest = CollectDebtRequest::query()
				->with(['collectDebtShare', 'collectDebtPartner'])
				->find($collectDebtWaitProcess->obj_id);

			/**
			 * Không có yêu cầu thì đánh dấu wait process là đã xử lý
			 */
			if (!$collectDebtRequest) {
				mylog(['Khong co thong tin yeu cau voi ID la:' => $collectDebtWaitProcess->obj_id]);
				$updatedVeDaXuLy = $collectDebtWaitProcess->forceFill([
					'status' => CollectDebtEnum::WAIT_PROCESS_STT_DA_XU_LY,
					'time_updated' => now()->timestamp
				])->update();

				if (!$updatedVeDaXuLy) {
					mylog(['Loi cap nhat update da xu ly' => 'ok']);
					throw new Exception('Khong tim duoc yeu cau va update waitprocess khong thanh cong');
				}

				DB::commit();
				return $collectDebtWaitProcess;
			}

			/**
			 * Có yêu cầu và yêu cầu đó đã có partner thì không được hủy
			 * Đánh dấu waitProcess về đã xử lý
			 */
			if ($collectDebtRequest->collectDebtPartner) {
				mylog([
					'[LOI] - yeu cau da co partner' => @optional($collectDebtRequest)->collectDebtPartner
				]);

				$updateProcessVeTrangThaiDuocHuy = CollectDebtWaitProcess::query()
					->where('id', $collectDebtWaitProcess->id)
					->update(['status' => CollectDebtEnum::WAIT_PROCESS_STT_DUOC_HUY]);

				mylog(['Ket qua cap nhat wait process la huy, ko dung nua' => $updateProcessVeTrangThaiDuocHuy]);
				DB::commit();
				return $collectDebtWaitProcess;
			}

			/**
			 * Kiểm tra lệnh nguồn (VA/IB_OFF) đã ghi sổ và hạch toán hay chưa
			 * Nếu chưa thì throw lỗi ra
			 */
			$yeuCauThuBiDong = CollectDebtRequest::query()->with('collectDebtLedger')->find($collectDebtWaitProcess->source_id);
			
			if (!$yeuCauThuBiDong->collectDebtLedger || $yeuCauThuBiDong->collectDebtLedger->status != CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN) {
				mylog(['khong the thuc hien do yc thu VA, IBOFF chua hach toan' => 'ok']);
				throw new Exception('Process chua the thuc hien do loi logic');
			}

			if (empty($collectDebtRequest->partner_transaction_id)) {
				mylog([
					'Yeu cau bi huy la' => $collectDebtRequest->partner_request_id,
					'Yeu cau muon huy khong co ma chung tu' => 'ok'
				]);

				throw new Exception('Yeu cau muon huy khong co ma chung tu, ma yc la: ' . $collectDebtRequest->partner_request_id);
			}

			/**
			 * Gọi lệnh check
			 * 	1-Đang là CANCEL rồi thì dừng luồng xử lý
			 * 	2-Đang khác CANCEL hoặc chưa có thông tin thì gọi hủy lệnh
			 */
			$checkResult = app(CheckRequestViaMposSubAction::class)->run(
				$collectDebtRequest,
				request(),
				true
			);

			mylog(['checkResult' => $checkResult]);

			// 1-Đang là CANCEL rồi thì dừng luồng xử lý
			if (!empty($checkResult['data']['status']) && $checkResult['data']['status'] == 'CANCEL') {
				$this->handleCancelComplete($collectDebtRequest, $collectDebtWaitProcess);
				DB::commit();
			}

			// 2. Nếu đang là Apporved thì kết lúc luồng hiện tại và chấp nhận thu thừa
			if (!empty($checkResult['data']['status']) && ($checkResult['data']['status'] == 'APPROVED' || $checkResult['data']['status'] == 'SUCCESS')) {
				$collectDebtWaitProcess->status = CollectDebtEnum::WAIT_PROCESS_STT_DA_XU_LY;
				$collectDebtWaitProcess->time_updated = now()->timestamp;
				$collectDebtWaitProcess->save();
				DB::commit();
				return $collectDebtWaitProcess;
			}

			if (empty($checkResult['data']['status']) || $checkResult['data']['status'] == 'TIMEOUT') {
				throw new Exception('Lệnh đang trong trạng thái TIMEOUT, không thể hủy');
			}

			// 2-Đang khác CANCEL hoặc chưa có thông tin thì gọi hủy lệnh
			$cancelMposRequest = app(DebtRecoveryRequestCancelPaymentAction::class)->run($collectDebtRequest);
			mylog(['cancelMposRequest' => $cancelMposRequest]);

			if (!empty($cancelMposRequest['data']['error_code']) && $cancelMposRequest['data']['error_code'] == '00') {
				$this->handleCancelComplete($collectDebtRequest, $collectDebtWaitProcess);
				DB::commit();
			} else {
				$msg = sprintf('Hệ thống không hủy được lệnh trích: %s ở luồng waitProcess', $collectDebtRequest->partner_request_id);
				throw new Exception($msg);
			}

			return $collectDebtWaitProcess;
		} catch (\Throwable $th) {
			DB::rollBack();

			$msg = sprintf(
				'env: %s --- WaitProcessId: %s ---- Err: %s',
				config('app.env'),
				$collectDebtWaitProcess->id,
				$th->getMessage()
			);
			
			@TelegramAlert::alertCancelMposKhiCoNguonTienVe($msg);

			// Update về chưa xử lý để job sau xử lý tiếp
			$updatedVeChuaXuLy = CollectDebtWaitProcess::query()
				->where('id', $collectDebtWaitProcess->id)
				->where('status', CollectDebtEnum::WAIT_PROCESS_STT_DANG_XU_LY)
				->update(['status' => CollectDebtEnum::WAIT_PROCESS_STT_CHUA_XU_LY]);

			if (!$updatedVeChuaXuLy) {
				mylog(['[LOI] Update ve chua xu ly' => 'ok']);
			}

			throw new Exception(Helper::traceError($th));
		}
	}

	public function handleCancelComplete(CollectDebtRequest $collectDebtRequest, CollectDebtWaitProcess $collectDebtWaitProcess)
	{
		$coreContract = $collectDebtRequest->collectDebtShare->getCoreContractByGuide();
		$partnerKhongDong = app(CreatePartnerKhongDongDoChuDongHuySubAction::class)->run($collectDebtRequest, $coreContract);
		mylog(['partner khong dong' => $partnerKhongDong]);

		if ($partnerKhongDong) {
			$collectDebtWaitProcess->status = CollectDebtEnum::WAIT_PROCESS_STT_DA_XU_LY;
			$collectDebtWaitProcess->time_updated = now()->timestamp;
			$saveResult = $collectDebtWaitProcess->save();

			if (!$saveResult) {
				throw new Exception('khong cap nhat duoc wait process');
			}

			$updateYeuCauMpos = CollectDebtRequest::query()->where('id', $collectDebtRequest->id)->update([
				'time_canceled' => now()->timestamp,
				'canceled_by' => Helper::getCronJobUser(),
			]);

			if (!$updateYeuCauMpos) {
				mylog(['khong cap nhat duoc yc ve trang thai da huy' => $collectDebtRequest->id]);
				throw new Exception('khong cap nhat duoc yeu cau ve trang thai da huy');
			}

			return true;
		}

		$msg = 'Lỗi: không tạo được partner 0đ';
		throw new Exception($msg);
	}
} // End class
