<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction;

use Exception;
use Illuminate\Support\Facades\Redis;
use App\Modules\CollectDebt\Enums\CacheEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\CheckRequestViaMposSubAction;

class CheckRequestViaRedisAction
{
	private int $__limit = 30;

	public function run()
	{
		$request = request();

		$now = now()->timestamp;
		$key = CacheEnum::NLV4_REDIS_LIST_LENH_TRICH_CAN_CHECK;

		$requestIds = Redis::zrangebyscore($key, '-inf', $now, [
			'limit' => [0, $this->__limit]
		]); // score <= now

		if (empty($requestIds)) {
			return "Khong co yeu cau nao can check";
		}

		$collectDebtRequests = CollectDebtRequest::query()
			->with('collectDebtPartner')
			->whereIn('partner_request_id', $requestIds)
			->get();

		throw_if($collectDebtRequests->isEmpty(), new Exception('Không có yêu cầu cân check trạng thái trích nợ'));

		$collectDebtRequests->each(function (CollectDebtRequest $collectDebtRequest) use ($request) {
			$partnerRequestId = $collectDebtRequest->partner_request_id;

			// chua co partner thi goi check
			if (!$collectDebtRequest->collectDebtPartner) {
				$collectDebtRequestChecked = app(CheckRequestViaMposSubAction::class)->run($collectDebtRequest, $request);
				$collectDebtRequest->mpos_debt_result = $collectDebtRequestChecked;
			}

			// co partner roi thi update time_checked
			if ($collectDebtRequest->collectDebtPartner) {
				$collectDebtRequest->forceFill(['time_checked' => time()])->update();
			}

			return $collectDebtRequest;
		});

		return $collectDebtRequests->pluck(['partner_request_id']);
	}

	// public function handleCheckResult(CollectDebtRequest $collectDebtRequest, $checkDebtResult = [])
	// {
	// 	// có partner
	// 	if ($collectDebtRequest->collectDebtPartner) {
	// 		$this->updateTimeChecked($collectDebtRequest);
	// 		return;
	// 	}

	// 	// không có kết quả check
	// 	if (empty($checkDebtResult['RespCode']) || empty($checkDebtResult['data'])) {
	// 		$this->updateTimeChecked($collectDebtRequest);
	// 		return;
	// 	}

	// 	// Có response code
	// 	$nextlendServiceData = $this->safeJsonDecode($checkDebtResult['data']);
	// 	if (!$nextlendServiceData || empty($nextlendServiceData['data'])) {
	// 		$this->updateTimeChecked($collectDebtRequest);
	// 		return;
	// 	}

	// 	$mposResultData = $this->safeJsonDecode($nextlendServiceData['data']);
	// 	if (!$mposResultData || empty($mposResultData['data'])) {
	// 		$this->updateTimeChecked($collectDebtRequest);
	// 		return;
	// 	}

	// 	// mpos trả kết quả check bị lỗi
	// 	if (empty($mposResultData['status'])) {
	// 		$this->updateTimeChecked($collectDebtRequest);
	// 		return;
	// 	}

	// 	// Check có thông tin
	// 	$mposDebtStatus = $mposResultData['data']['debtStatus'];

	// 	switch ($mposDebtStatus) {
	// 		case 'PENDING':
	// 			$this->updateTimeChecked($collectDebtRequest);
	// 			return;

	// 		case 'APPROVED':
	// 			$amountSuccess = $mposResultData['data']['debtRecoveryAmount'];
	// 			$this->createPartnerRecord($collectDebtRequest, $amountSuccess, $mposResultData);
	// 			break;

	// 		case 'CANCEL':
	// 		case 'EXPIRED':
	// 			$amountSuccess = 0;
	// 			$this->createPartnerRecord($collectDebtRequest, $amountSuccess, $mposResultData);
	// 			break;

	// 		case 'TIMEOUT':
	// 			// sẽ call lại
	// 			return;

	// 		default:
	// 			$this->updateTimeChecked($collectDebtRequest);
	// 			return;
	// 	}
	// }
}  // End class