<?php

namespace App\Modules\CollectDebt\Resources;

use Illuminate\Support\Str;
use Illuminate\Http\Resources\Json\ResourceCollection;

class CollectDebtRequestResourceCollection extends ResourceCollection
{
	public function toArray($request)
	{
		$isNeedFullPage = Str::contains($request->path(), 'DebtRecoveryRequestSearchData');
		
		return [
			'data' => $this->collection->toArray(),
			'meta' => [
				'current_page' => $this->currentPage(),
				'from'         => $this->firstItem(),
				'last_page'    => $isNeedFullPage ? $this->lastPage() : '',
				'path'         => "", // $this->path()
				'per_page'     => $this->perPage(),
				'to'           => $this->lastItem(),
				'total'        => $isNeedFullPage ? $this->total() : '',
			]
		];
	}

	public function toResponse($request)
	{
		return response()->json($this->toArray($request));
	}
}
