<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryGetAmountCanRefundAction;

use App\Modules\CollectDebt\Model\CollectDebtSummary;
use Exception;

class DebtRecoverySummaryGetAmountCanRefundAction
{
	public function run($request, $collectDebtSummary=null)
	{
		throw_if(!isset($request['contract_code']) || empty($request['contract_code']), new Exception('Trường contract_code không được để trống'));

		if (!$collectDebtSummary) {
			$collectDebtSummary = CollectDebtSummary::where('contract_code', $request['contract_code'])->first();
			
			if (!$collectDebtSummary) {
				throw new Exception('Không tìm thấy thông tin tổng hợp của HĐ này');
			}
		}
		
		$debtRevocerySummary = $collectDebtSummary->toArray();

		$totalAmountPaid = ($debtRevocerySummary['total_amount_paid'] + $debtRevocerySummary['total_fee_paid']);
		
		/**
		 * Cong thuc so tien co the hoan thu thua:
		 * 		R = Tong tien nhan 	- (Tong tien goc da thu + Tong phi da thu)
		 * 												- Tien thu thua da hoan
		 * 												- So tien da dung de thanh toan tien
		 * 												- So tien dang hoan <khong care nguon hoan la nguon nao, vi ko the tao yc hoan khi so nay > 0>
		 * 										
		 */
		$totalAmount = $debtRevocerySummary['total_amount_receiver'] - $totalAmountPaid 
																																 - $debtRevocerySummary['total_amount_excess_refund'] 
																																 - $debtRevocerySummary['total_amount_repayment_debt'] 
																																 - $debtRevocerySummary['amount_refunding'];
		mylog(['So tien co the hoan la' => $totalAmount]);
		return [
			'total_amount_can_refund' => $totalAmount
		];
	}
}
