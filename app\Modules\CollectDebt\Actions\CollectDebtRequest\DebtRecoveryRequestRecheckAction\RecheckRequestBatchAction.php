<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestRecheckAction;

use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtRequestOnly;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestRecheckAction\SubAction\HandleRecheckResultSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\CheckRequestViaMposSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCancelPaymentAction\DebtRecoveryRequestCancelPaymentAction;

class RecheckRequestBatchAction extends BatchProcessingAction
{
	public function run()
	{
		$startTime = microtime(true);

		$client = $this->createHttpClient();

		CollectDebtRequestOnly::query()
			->join('debt_recovery_ledger', 'debt_recovery_ledger.request_id', '=', 'debt_recovery_request.id')
			->where('payment_method_code', 'MPOS')
			->where('debt_recovery_request.status', CollectDebtEnum::REQUEST_STT_CAN_HOAN_THANH_VA_KIEM_TRA)
			->where('debt_recovery_ledger.status', CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN)
			->whereNotIn('debt_recovery_request.partner_request_id', $this->__ignoreIds)
			->select([
				'debt_recovery_request.partner_request_id',
				'debt_recovery_request.contract_code',
				'debt_recovery_request.status',
			])
			->chunkById(self::CHUNK_LIMIT, function (Collection $listRequestCanKiemTra) use ($client, $startTime) {
				if ((microtime(true) - $startTime) > self::BATCH_LIMIT_IN_SECONDS) {
					$this->__errorIds[] = "Times up";
					return false;
				}

				sleep(2);

				$this->processBatch($client, $listRequestCanKiemTra);

				unset($listRequestCanKiemTra);
			}, 'debt_recovery_request.id');
		
		return [
			'errors' => $this->__errorIds
		];
	}


	private function processBatch(Client $client, Collection $listRequest)
	{
		$baseUrl = config('app.url');

		// Generator
		$requests = function () use ($listRequest, $baseUrl) {
			foreach ($listRequest as $rq) {
				$url = $baseUrl . '/HandleRecheckRequest/' . $rq->partner_request_id;
				yield $rq->partner_request_id => new Request('POST', $url);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => self::POOL_CONCURRENCY,
			'fulfilled' => function ($response, string $partnerRequestId) {
				// $body = (string)$response->getBody();
			},
			'rejected' => function (\Throwable $reason, string $partnerRequestId) {
				$msg = "[ReCheckRequest --->$partnerRequestId] failed: " . $reason->getMessage();
				Log::info($msg);
				$this->__errorIds[] = $partnerRequestId;
				$this->__ignoreIds[] = $partnerRequestId; // ban ghi bi loi se tam ignore
			},
		]);

		$promise = $pool->promise();
		$promise->wait();

		unset($pool, $promise);
	}

	public function HandleRecheckRequest(string $partnerRequestId)
	{
		$request = request();

		$collectDebtRequest = CollectDebtRequest::query()->firstWhere(['partner_request_id' => $partnerRequestId]);

		if (!$collectDebtRequest) {
			return 'Request NotFound';
		}

		if ($collectDebtRequest->status != CollectDebtEnum::REQUEST_STT_CAN_HOAN_THANH_VA_KIEM_TRA) {
			return 'Request is processed';
		}

		$checkResult = app(CheckRequestViaMposSubAction::class)->checkWithoutAnyAction($collectDebtRequest, $request);

		if (empty($checkResult['data']['status'])) {
			return 'Check return no result';
		}

		// Nếu đang là pending thì gọi cancel ngay lập tức
		if ( !empty($checkResult['data']['status']) ) {
			if ($checkResult['data']['status'] == 'PENDING') {
				$cancel = app(DebtRecoveryRequestCancelPaymentAction::class)->run($collectDebtRequest);
				return $cancel;
			}

			return app(HandleRecheckResultSubAction::class)->run($checkResult, $collectDebtRequest);
		}

		return $partnerRequestId;
	}
}  // End class