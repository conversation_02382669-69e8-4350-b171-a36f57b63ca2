<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSetStatusAction;

use Exception;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestApproveStep2Request;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction\KiemTraVaDanhDauHuyLenhMposNeuCoTask;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAction\DebtRecoveryRequestActionCreateAction\DebtRecoveryRequestActionCreateAction;

class DebtRecoveryRequestApproveStep2Action
{
  private string $__apiMessage = 'Đã duyệt đề xuất giảm phí bước 2';

  public function run(DebtRecoveryRequestApproveStep2Request $request): CollectDebtRequest
  {
    $collectDebtRequest = CollectDebtRequest::find($request->json('data.id'));

    throw_if(!$collectDebtRequest, new Exception('Không có thông tin yêu cầu'));
    throw_if(empty($collectDebtRequest->approved_by), new Exception('Thao tác bị từ chối do yêu cầu này CHƯA ĐƯỢC DUYỆT bước 1'));
    throw_if(!empty($collectDebtRequest->isNguoiDuyet2()), new Exception('Thao tác bị từ chối do yêu cầu này ĐÃ ĐƯỢC DUYỆT bước 2'));
    throw_if(!$collectDebtRequest->isTrichTayGiamPhi(), new Exception('Thao tác bị từ chối do yêu cầu này KHÔNG PHẢI LÀ YÊU CẦU TRÍCH TAY GIẢM PHÍ'));

    $otherData = json_decode($collectDebtRequest->other_data, true);
    $approveStep2By = !empty($request->json('data.approved_step2_by')) ? json_decode($request->json('data.approved_step2_by'), true) : '';
    if (!empty($otherData) && is_array($otherData)) {
      $otherType = collect($otherData)->where('type', 'OTHER')->where('data.manual_request_type', '!=', '')->first();
      if (!empty($otherType)) {
        $otherType['data']['time_approved_step2'] = time();
        $otherType['data']['approved_step2_by'] = $approveStep2By;
        $otherType['time_modified'] = time();
        $otherData = collect($otherData)->map(function ($item) use ($otherType) {
          if (isset($item['data']['manual_request_type']) && !empty($item['data']['manual_request_type'])) {
            $item['data'] = array_merge($item['data'], $otherType['data']);
          }
          return $item;
        });
      } else {
        $type = [
          'data' => [
            'time_approved_step2' => time(),
            'approved_step2_by' => $approveStep2By
          ],
          'note' => 'Dữ liệu liên quan đến yêu cầu',
          'type' => 'OTHER',
          'time_modified' => time(),
        ];
        array_push($otherData, $type);
      }
    } else {
      $otherData = [];
      $type = [
        'data' => [
          'time_approved_step2' => time(),
          'approved_step2_by' => $approveStep2By
        ],
        'note' => 'Dữ liệu liên quan đến yêu cầu',
        'type' => 'OTHER',
        'time_modified' => time(),
      ];
      array_push($otherData, $type);
    }
    

    $collectDebtRequest->other_data = json_encode($otherData, JSON_UNESCAPED_UNICODE);
    $collectDebtRequest->save();

    $paramAction = [
      'type' => CollectDebtEnum::REQUEST_ACTION_TYPE_GIAM_PHI,
      'request_id' => $collectDebtRequest->id,
      'action_code' => CollectDebtEnum::REQUEST_ACTION_CODE_APPROVE2,
      'created_by' => $request->json('data.approved_step2_by') ?? "",
      'description' => $request->json('data.description') ?? "",
      'other_data' => '{}',
      'time_created' => time(),
    ];

    $action = app(DebtRecoveryRequestActionCreateAction::class)->run($paramAction);
		
		if (!$action) {
			mylog(['Loi khong tao duoc ban ghi RequestAction' => $action]);
			throw new Exception('Loi khong tao duoc ban ghi RequestAction');
		}

		/**
		 * Update 29.07.2024: Trong trường hợp yc giảm phí được tạo ra nhưng hết thời gian dừng job
		 * vẫn chưa được duyệt, rất có thể hệ thống sẽ sinh ra lệnh MPOS để thu tiền tự động.
		 * Đoạn này phải bắt nếu có lệnh MPOS đang chạy thì sẽ hủy nó đi
		 */
		$waitProcess = app(KiemTraVaDanhDauHuyLenhMposNeuCoTask::class)->run($collectDebtRequest->contract_code, $collectDebtRequest);
		
    $collectDebtRequest->__apiMessage = $this->__apiMessage;

    return $collectDebtRequest;
  }
} // End class