<?php

/**
 * Created by PhpStorm.
 * User: nam<PERSON><PERSON>en
 * Date: 31/05/21
 * Time: 13:15
 */

namespace App\Modules;

use Illuminate\Support\Facades\File;
use Illuminate\Support\ServiceProvider;
//use File;

class ModuleServiceProvider extends ServiceProvider
{

    public function register()
    {

        // Khai báo configs
        $configFile = [
            'collect_debt_gateway_config' => __DIR__.'/CollectDebtGateway/configs/collect_debt_gateway_config.php',
            'collect_debt_email_remind_config' => __DIR__.'/EmailRemind/configs/collect_debt_email_remind_config.php',
        ];
        foreach ($configFile as $alias => $path) {
            $this->mergeConfigFrom($path, $alias);
        }
    }

    public function boot()
    {
        // Đăng ký modules theo cấu trúc thư mục
        $directories = array_map('basename', \Illuminate\Support\Facades\File::directories(__DIR__));
        foreach ($directories as $moduleName) {
            $this->registerModule($moduleName);
        }
    }

    // Khai báo đăng ký cho từng modules
    private function registerModule($moduleName)
    {
        $modulePath = __DIR__ . "/$moduleName/";
        // Khai báo thành phần ở đây
        // Khai báo route
        if (\Illuminate\Support\Facades\File::exists($modulePath . "routes/routes.php")) {
            $this->loadRoutesFrom($modulePath . "routes/routes.php");
        }


        // Khai báo helpers
        if (\File::exists($modulePath . "Ultilities/index.php")) {
          require $modulePath . "Ultilities/index.php";
        }
    }
}
