<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtSchedule;

use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Resources\CollectDebtScheduleResourceCollection;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSchedule\DebtRecoveryContractPlanUpdateRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSchedule\DebtRecoveryContractPlanGetByIdRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSchedule\DebtRecoveryContractPlanSetStatusRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSchedule\DebtRecoveryContractPlanGetByContractCodeRequest;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateAction\DebtRecoveryContractPlanCreateAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanUpdateAction\DebtRecoveryContractPlanUpdateAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanGetByIdAction\DebtRecoveryContractPlanGetByIdAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanSetStatusAction\DebtRecoveryContractPlanSetStatusAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanSearchDataAction\DebtRecoveryContractPlanSearchDataAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanGetByContractCodeAction\DebtRecoveryContractPlanGetByContractCodeAction;

class CollectDebtScheduleController extends Controller
{
	public function searchData(Request $request)
	{
		try {
			$collectDebtSchedule = app(DebtRecoveryContractPlanSearchDataAction::class)->run($request);
			$collectDebtScheduleResource = new CollectDebtScheduleResourceCollection($collectDebtSchedule);
			$response = $collectDebtScheduleResource->toResponse($request)->getData(true);
			return $this->successResponse($response, $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}


	public function planCreate(DebtRecoveryContractPlanCreateRequest $request)
	{
		try {
			$collectDebtScheduleArray = app(DebtRecoveryContractPlanCreateAction::class)->run($request->json('data.schedules'));
			return $this->successResponse($collectDebtScheduleArray, $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function planUpdate(DebtRecoveryContractPlanUpdateRequest $request)
	{
		try {
			$collectDebtSchedule = app(DebtRecoveryContractPlanUpdateAction::class)->run($request);
			return $this->successResponse($collectDebtSchedule->toArray(), $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function getById(DebtRecoveryContractPlanGetByIdRequest $request)
	{
		try {
			$collectDebtSchedule = app(DebtRecoveryContractPlanGetByIdAction::class)->run($request);
			return $this->successResponse($collectDebtSchedule->toArray(), $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function setStatus(DebtRecoveryContractPlanSetStatusRequest $request)
	{
		try {
			$collectDebtSchedule = app(DebtRecoveryContractPlanSetStatusAction::class)->run($request);
			return $this->successResponse($collectDebtSchedule->toArray(), $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function getByContractCode(DebtRecoveryContractPlanGetByContractCodeRequest $request) {
		try {
			$plans = app(DebtRecoveryContractPlanGetByContractCodeAction::class)->run($request);
			return $this->successResponse($plans, $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
