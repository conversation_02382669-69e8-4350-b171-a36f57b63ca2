<?php

namespace App\Modules\CollectDebt\Resources;

use Illuminate\Support\Str;
use Illuminate\Http\Resources\Json\ResourceCollection;

class CollectDebtGenFeeResourceCollection extends ResourceCollection
{

	public function toArray($request)
	{
		return [
			'data' => $this->collection->map(function ($item) {
				return $this->formatGenFeeItem($item);
			}),
			'links' => [],
			'meta' => []
		];
	}

	/**
	 * Format từng item trong collection
	 */
	private function formatGenFeeItem($item)
	{
		return [
			'id' => $item->id,
			'contract_code' => $item->contract_code,
			'ledger_id' => $item->ledger_id,
			'fee_type' => $item->fee_type,
			'fee_amount' => $item->fee_amount,
			'time_gen_fee' => $item->time_gen_fee,
			'time_created' => $item->time_created,
			'time_updated' => $item->time_updated,
			'status' => $item->status,
			'profile_data' => $this->buildProfileData($item->collectDebtShareOnly->profile_data)
		];
	}

	private function buildProfileData($profileData)
	{
		$profileDataArray = json_decode($profileData, true);
		return [
			'fullname' => $profileDataArray['merchant']['fullname'],
			'email' => $profileDataArray['merchant']['email'],
			'mobile' => mask_phone($profileDataArray['merchant']['mobile']),
			'address' => $profileDataArray['merchant']['address'],
		];
	}
} // End class
