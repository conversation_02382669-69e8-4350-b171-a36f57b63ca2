<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\GenFeeReportAction;

use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Model\CollectDebtGenFee;
use App\Modules\CollectDebt\Model\CollectDebtLateFee;

class MoveLateFeeToGenFeeAction
{
	const FEE_PHAT_TRA_CHAM = 3;

	public function run()
	{
		return CollectDebtLateFee::query()
			->where('status', 1)
			->chunkById(50, function (Collection $listLedger) {
				$this->handle($listLedger);
			});
	}

	public function handle(Collection $listLateFee)
	{
		$currentTimestamp = now()->timestamp;

		foreach ($listLateFee as $lateFee) {
			$input = [
				'contract_code' => $lateFee->contract_code,
				'fee_type' => self::FEE_PHAT_TRA_CHAM,
				'fee_amount' => $lateFee->total_fee,
				'time_gen_fee' => strtotime($lateFee->created_at),
				'time_created' => $currentTimestamp,
				'time_updated' => $currentTimestamp,
			];

			$collectDebtGenFee = CollectDebtGenFee::query()->firstOrCreate([
				'contract_code' => $lateFee->contract_code,
				'fee_type' => self::FEE_PHAT_TRA_CHAM,
			], $input);

			if (!$collectDebtGenFee) {
				throw new \Exception('Loi insert gen fee');
			}

			$lateFee->status = 3;
			$lateFee->save();
		}

		return true;
	}
}  // End class