<?php

namespace App\Modules\CollectDebt\Actions;

use App\Lib\Helper;
use GuzzleHttp\Client;

class BatchProcessingAction
{
	public const CHUNK_LIMIT = 15;
	public const POOL_CONCURRENCY = 2; 
	public const HTTP_TIMEOUT = 10;
	public const BATCH_LIMIT_IN_SECONDS = 50; 
	public const MAX_QOUTA = 45;

	public array $__processedIds = [];
	public array $__errorIds = [];
	public array $__ignoreIds = [];

	public int $done = 0;
	public int $failed = 0;

	public int $qouta = 0;


	// for các batch độc lập
	public int $batchCount = 4;
	public int $batchSize = 30;
	public int $min = 0;
	public int $max = 0;

	public function createHttpClient($timeout=self::HTTP_TIMEOUT): Client
	{
		return new Client([
			'base_uri' => config('app.url'),
			'connect_timeout' => config('nextlend.NL_PARTNER_CONNECTION_TIMEOUT', 3),
			'timeout' => $timeout,
			'http_errors'     => false, 
			'verify'          => false,
			'headers'         => [
					'Connection' => 'keep-alive'
			],
		]);
	}

	public function canRunJob(bool $checkViaCutOffTime=true): bool
	{
		if (!$checkViaCutOffTime) {
			return true;
		}
		
		$now = now();
		$cutOffTime = today()->setTimeFromTimeString(Helper::getCutOffTime());
		

		if ($now->gte($cutOffTime)) {
			return false;
		}

		return true;
	}
} // End class
