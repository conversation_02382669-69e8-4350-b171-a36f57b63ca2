<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction\Task;

use App\Lib\Helper;
use App\Lib\ApiCall;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\CheckRequestViaMposSubAction;
use App\Utils\CommonVar;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;

class XuLyYeuCauGuiLaiQuaMposTask
{
  /**
   * Bối cảnh: y/c hiện tại đã gửi 1 lần trước đó và bị đánh là LỖI RÕ RÀNG
   * B1: Gửi lại qua MPOS
   * B2: Trả về kết quả là trùng
   *
   * @param CollectDebtRequest $collectDebtRequest
   * @return CollectDebtRequest
   */
  public function run(CollectDebtRequest $collectDebtRequest): CollectDebtRequest
  {
    // Lúc này do chưa có partner_transaction_id, gọi luồng check không ghi công nợ để lấy partner_transaction_id
    $checkDebtResult = app(CheckRequestViaMposSubAction::class)->run($collectDebtRequest, request(), true);

    if (!empty($checkDebtResult['data']['partner_transaction_id'])) {
      $collectDebtRequest->partner_transaction_id = $checkDebtResult['data']['partner_transaction_id'];

      $collectDebtRequest->sended_by = Helper::getCronJobUser();
      $collectDebtRequest->time_sended = time();
      $collectDebtRequest->status = CollectDebtEnum::REQUEST_STT_DA_DUYET;
      $collectDebtRequest->status_payment = CollectDebtEnum::REQUEST_STT_PM_DA_GUI;

      $collectDebtRequest->time_checked = time(); // Thêm cái này để 30' sau hẵng check

      if (empty($collectDebtRequest->time_approved)) {
        $collectDebtRequest->time_approved = time();
        $collectDebtRequest->approved_by = Helper::getCronJobUser();
      }

      $collectDebtRequest->save();
      return $collectDebtRequest;
    }

    return $collectDebtRequest;
  }
} // End class