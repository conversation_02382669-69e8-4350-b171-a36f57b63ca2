<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtContractEvent;

use App\Lib\Helper;

use Illuminate\Http\Request;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;

class CollectDebtContractEventController extends Controller
{
	public function deleteOldEvent(Request $request)
	{
		$timeline = now()->subDays(3)->timestamp;
		try {
			$deleted = CollectDebtContractEvent::query()->where('time_created', '<', $timeline)->delete();
			return $this->successResponse(['result' => $deleted], $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
