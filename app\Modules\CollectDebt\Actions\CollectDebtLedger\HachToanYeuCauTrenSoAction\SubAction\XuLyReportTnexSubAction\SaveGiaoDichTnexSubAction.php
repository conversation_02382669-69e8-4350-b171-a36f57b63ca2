<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyReportTnexSubAction;

use Exception;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtTnexTransaction;

class SaveGiaoDichTnexSubAction
{
	public function run(CollectDebtLedger $collectDebtLedger) {
		// Kiểm tra HĐ hiện tại có phải của đối tác tnex giải ngân hay không, nếu có thì lưu vào 
		// 1 bảng để sau còn đồng bộ sang cho bên đó
		$collectDebtShare = CollectDebtShare::query()->where('contract_code', $collectDebtLedger->contract_code)->first();
		
		if (!$collectDebtShare) {
			return;
		}

		$contractData = json_decode($collectDebtShare->contract_data, true);
		
		if (!empty($contractData['contract_partner']['partner_code']) && $contractData['contract_partner']['partner_code'] == 'TNEX') {
			$p = [
				'partner_code' => 'TNEX',
				'transaction_id' => $collectDebtLedger->collectDebtRequest->partner_request_id,
				'contract_code' => $collectDebtLedger->contract_code,
				'response_id' => null,
				'amount' => $collectDebtLedger->amount,
				'amount_response' => null,
				'partner_data' => json_encode([]),
				'other_data' => json_encode([]),
				'status' => CollectDebtTnexTransaction::MOI_TAO,
				'time_created' => now()->timestamp,
				'time_updated' => now()->timestamp
			];
	
			$collectDebtTnextTran = CollectDebtTnexTransaction::query()->firstOrCreate([
				'transaction_id' => $collectDebtLedger->collectDebtRequest->partner_request_id
			], $p);
			if (!$collectDebtTnextTran) {
				throw new Exception('Loi khong luu duoc giao dich dong bo sang Tnex');
			}
	
			return $collectDebtTnextTran;
		}

		return;
	}
} // End class