<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtGuide;

use Carbon\Carbon;
use App\Lib\TelegramAlert;
use App\Traits\ApiResponser;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Exceptions\ContractCodeExistException;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Rules\CollectDebtGuide\DayDuKenhThuRule;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use App\Modules\CollectDebt\Rules\CollectDebtGuide\ValidateListFeeRule;
use App\Modules\CollectDebt\Rules\CollectDebtGuide\CollectDebtTrichNgayRule;
use App\Modules\CollectDebt\Rules\CollectDebtGuide\ValidateContracPartnertRule;
use App\Modules\CollectDebt\Rules\CollectDebtGuide\PreventDuplicatePaymentMethodRule;
use App\Modules\CollectDebt\Rules\CollectDebtGuide\ValidateNgayBatDauHopDongQuaKhuRule;

class DebtRecoveryContractGuideCreateRequest extends FormRequest
{
	use ApiResponser;
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'], 
      'data.profile_id' => ['required', 'string', 'max:25'],
      'data.contract_code' => ['required', 'string', 'max:50', 'unique:debt_recovery_contract_guide,contract_code'],
      'data.contract_cycle' => ['required', 'numeric', 'min:1'],
      'data.contract_type' => [
        'required', 
        'numeric', 
        Rule::in([
          CollectDebtEnum::GUIDE_HD_TRICH_NGAY,
          CollectDebtEnum::GUIDE_HD_TRICH_KY,
          CollectDebtEnum::GUIDE_HD_GIA_HAN,
        ]),
        new CollectDebtTrichNgayRule(),
        function ($attribute, $value, $fail) {
          $partnerCode = request()->json('data.partner_code');
          if ($partnerCode == 'TNEX' && $value != CollectDebtEnum::GUIDE_HD_TRICH_NGAY) {
            $fail('Đối với đối tác TNEX, loại hợp đồng phải là trích ngày');
          }
        }
      ],
      'data.contract_intervals' => ['required', 'numeric', 'min:1'],
      'data.contract_time_start' => ['required', 'date_format:d-m-Y H:i:s', new ValidateNgayBatDauHopDongQuaKhuRule()],
      'data.contract_time_end' => ['required', 'date_format:d-m-Y H:i:s'],
      'data.amount' => ['required', 'numeric', 'min:1000000'],

      'data.payment_guide' => [
        'bail', 
        'required', 
        'array',  
        new PreventDuplicatePaymentMethodRule(),
        new DayDuKenhThuRule() // Phải có ít nhất 1 kênh thu: Default Require MPOS
      ],
      'data.payment_guide.*.payment_channel_code' => ['required', 'string'],
      'data.payment_guide.*.payment_account_id' => ['required', 'string'],
      'data.payment_guide.*.other_data' => ['required', 'array'],
      'data.payment_guide.*.other_data.payment_account_holder_name' => ['nullable', 'string', 'max:255'],
      'data.payment_guide.*.other_data.payment_account_branch' => ['nullable', 'string', 'max:255'],
      'data.payment_guide.*.other_data.payment_account_bank_code' => ['nullable', 'string', 'max:255'],

      'data.list_fee' => ['required', 'array', new ValidateListFeeRule(request()->json('data.contract_type'))],
      'data.list_fee.*.type' => [
        'required', 
        'numeric', 
        Rule::in([
          CollectDebtEnum::GUIDE_LOAI_PHI_HOP_DONG,
          CollectDebtEnum::GUIDE_LOAI_PHI_THAM_DINH_HO_SO,
          CollectDebtEnum::GUIDE_LOAI_PHI_QUA_HAN,
          CollectDebtEnum::GUIDE_LOAI_PHI_UU_DAI_THAM_DINH_HO_SO,
          CollectDebtEnum::GUIDE_LOAI_PHI_GIAI_NGAN,
          CollectDebtEnum::GUIDE_LOAI_PHI_THU_HOI,
          CollectDebtEnum::GUIDE_LOAI_PHI_GIA_HAN,
          CollectDebtEnum::GUIDE_LOAI_PHI_CHAM_KY,
          CollectDebtEnum::GUIDE_LOAI_UU_DAI_PHI_THAM_GIA,
          CollectDebtEnum::GUIDE_LOAI_PHI_HOAN,
          CollectDebtEnum::GUIDE_LOAI_PHI_PHAT_TRA_CHAM,
        ])
      ],

      'data.list_fee.*.percent_fee' => ['nullable', 'numeric', 'min:0'],
      'data.list_fee.*.flat_fee' => [ 'nullable', 'numeric', 'min:0'],
      'data.list_fee.*.fee_max' => [ 'nullable', 'numeric', 'min:0'],
      'data.list_fee.*.fee_min' => [ 'nullable', 'numeric', 'min:0'],

      'data.other_data' => ['required', 'json'],

      'data.contract_data' => ['required', 'array'],
      'data.contract_data.id' => ['required', 'numeric'],
      'data.contract_data.contract_code' => ['required', 'string'],
			'data.contract_data.users_admin' => ['array', 'required'],
			'data.contract_data.users_admin.mobile' => ['required', 'string'],
			'data.contract_data.users_admin.fullname' => ['required', 'string'],
			'data.contract_data.users_admin.email' => ['required', 'string'],
			
			// user_action
			'data.contract_data.user_action' => ['required', 'array'],
			
				// // for merchant  --> off theo nhu cầu của Hiền-BA
				// 'data.contract_data.user_action.merchant' => ['required', 'array'],
				// 'data.contract_data.user_action.merchant.*.id' => ['required', 'numeric', 'min:1'],
				// 'data.contract_data.user_action.merchant.*.name' => ['required', 'string'],
				// 'data.contract_data.user_action.merchant.*.code' => ['required', 'string'],
				// 'data.contract_data.user_action.merchant.*.user' => ['required', 'array'],
				// 'data.contract_data.user_action.merchant.*.user.*.id' => ['required', 'numeric', 'min:1'],
				// 'data.contract_data.user_action.merchant.*.user.*.username' => ['required', 'string'],
				// 'data.contract_data.user_action.merchant.*.user.*.email' => ['required', 'string'],
				// 'data.contract_data.user_action.merchant.*.user.*.mobile' => ['required', 'string'],
				// 'data.contract_data.user_action.merchant.*.user.*.fullname' => ['required', 'string'],

				// for contract
				'data.contract_data.user_action.contract' => ['required', 'array'],
				'data.contract_data.user_action.contract.*.id' => ['required', 'numeric', 'min:1'],
				'data.contract_data.user_action.contract.*.name' => ['required', 'string'],
				'data.contract_data.user_action.contract.*.code' => ['required', 'string'],
				'data.contract_data.user_action.contract.*.user' => ['required', 'array'],
				'data.contract_data.user_action.contract.*.user.*.id' => ['required', 'numeric', 'min:1'],
				'data.contract_data.user_action.contract.*.user.*.username' => ['required', 'string'],
				'data.contract_data.user_action.contract.*.user.*.email' => ['required', 'string'],
				'data.contract_data.user_action.contract.*.user.*.mobile' => ['required', 'string'],
				'data.contract_data.user_action.contract.*.user.*.fullname' => ['required', 'string'],

      'data.profile_data' => ['required', 'array'],
      'data.profile_data.id' => ['required', 'numeric'],

      'data.profile_data.borrower' => ['required', 'array'],
      'data.profile_data.borrower.id' => ['required', 'numeric'],
      'data.profile_data.borrower.merchant_id' => ['required', 'numeric', 'same:data.profile_data.merchant.id'],

      'data.profile_data.merchant' => ['required', 'array'],
      'data.profile_data.merchant.id' => ['required', 'numeric'],
      'data.profile_data.merchant.email' => ['required', 'string', 'max:255'],
      'data.profile_data.merchant.mobile' => ['required', 'string', 'max:255'],
      'data.profile_data.merchant.fullname' => ['required', 'string', 'max:255'],
      'data.profile_data.merchant.partner_merchant_code' => ['required', 'numeric'], // Mã ngnồn MC phải là dạng số

      'data.description' => ['nullable', 'string', 'max:255'],
      'data.created_by' => ['bail', 'required', 'string'],
			'data.partner_code' => ['required', 'string', 'max:15', 'in:NEXTLEND,TNEX', new ValidateContracPartnertRule()],
    ];
  }

  protected function passedValidation()
  {
    $params = $this->all();
    $params['data']['status'] = CollectDebtEnum::GUIDE_STT_MOI_TAO;

    $params['data']['payment_guide'] = json_encode($params['data']['payment_guide']);
    $params['data']['list_fee'] = json_encode($params['data']['list_fee'], JSON_UNESCAPED_UNICODE);
    $params['data']['profile_data']['id'] = (string) $params['data']['profile_data']['id'];
    $params['data']['profile_data'] = json_encode($params['data']['profile_data'], JSON_UNESCAPED_UNICODE);
    $params['data']['contract_data'] = json_encode($params['data']['contract_data'], JSON_UNESCAPED_UNICODE);
    
    $params['data']['time_created'] = time();
    $params['data']['time_approved'] = 0;
    $params['data']['time_canceled'] = 0;
    $params['data']['time_create_calendar'] = 0;

    $params['data']['contract_time_start'] = Carbon::createFromFormat('d-m-Y H:i:s', $params['data']['contract_time_start'])->timestamp;
    $params['data']['contract_time_end'] = Carbon::createFromFormat('d-m-Y H:i:s', $params['data']['contract_time_end'])->timestamp;

    $this->merge($params);
  }

  protected function prepareForValidation()
  {
    $params = $this->all();
    $params['data']['created_by'] = StandardizedDataFilter::getStandardizedDataFilter('USER_ADMIN', $params['data']['created_by']);
    $this->merge($params);
  }

  public function messages()
  {
    return [
      'data.profile_data.borrower.merchant_id.same' => 'data.profile_data.borrower.merchant_id đang khác với data.profile_data.merchant.id'
    ];
  }

  protected function failedValidation(Validator $validator)
  {
   
		/**
		 * array:1 [
				"data.contract_code" => array:1 [
					"Unique" => array:2 [
						0 => "debt_recovery_contract_guide"
						1 => "contract_code"
					]
				]
			]
		 */
		$failedRules = $validator->failed();
		
		/**
		 * array:1 [
				0 => "data.contract_code"
			]
		 */
		$failedFields = $validator->errors()->keys();

		$errorMessage = [
      'Hợp đồng' => $this->json('data.contract_code'),
      'Error' => $validator->errors()->first(),
      'LogID' => $this->json('api_request_id')
    ];

		TelegramAlert::sendDayChiDan($errorMessage);

		foreach ($failedFields as $field) {
			if ($field == 'data.contract_code' && !empty($failedRules[$field]['Unique'])) {
				$message = sprintf('Hợp đồng `%s` đã tồn tại trong hệ thống', $this->json('data.contract_code'));
				
				throw new ContractCodeExistException(
					['errors' => $validator->errors()->all()], 
					409, 
					$message
				);
			}
		}
		
    return parent::failedValidation($validator);
  }
} // End class
