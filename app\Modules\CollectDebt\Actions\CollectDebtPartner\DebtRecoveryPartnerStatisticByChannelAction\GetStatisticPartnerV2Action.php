<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerStatisticByChannelAction;

use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Model\CollectDebtConfigAuto;
use App\Modules\CollectDebt\Model\CollectDebtStatisticPartner;
use App\Modules\CollectDebt\Requests\v1\CollectDebtGuide\DebtRecoveryContractGuideGetPaymentStatisticRequest;

class GetStatisticPartnerV2Action
{
	private int $__totalRequest = 0;

	public array $returnData = [];

	public function run(DebtRecoveryContractGuideGetPaymentStatisticRequest $request): array
	{
		$contractCode = trim($request->json('data.contract_code', ''));
		$statisticPartner = CollectDebtStatisticPartner::query()->where('contract_code', $contractCode)->first();

		// Chưa được đồng bộ
		if (optional($statisticPartner)->status_sync != 1) {
			$statisticChannel = app(DebtRecoveryPartnerStatisticByChannelAction::class)->run($request);

			$input = ['contract_code' => $contractCode, 'status_sync' => 1, 'time_sync' => time()];

			foreach ($statisticChannel as $channel => $items) {
				$collect = collect($items);
				$tongSoLenh = $collect->where('label', 'so_lenh')->first()['value'] ?? 0;
				$tongTienNhan = $collect->where('label', 'tong_tien_nhan')->first()['value'] ?? 0;
				$daTrich = $collect->where('label', 'da_trich')->first()['value'] ?? 0;
				$daHoan = $collect->where('label', 'da_hoan')->first()['value'] ?? 0;
				$dangCo = $collect->where('label', 'dang_co')->first()['value'] ?? 0;

				if ($channel == 'MPOS') {
					$input['mpos_total_request'] = $tongSoLenh;
					$input['mpos_total_receipt'] = $tongTienNhan;
					$input['mpos_total_repayment'] =  $daTrich;
					$input['mpos_total_refund'] = $daHoan;
					$input['mpos_total_money_have'] =  $dangCo;
				}

				if ($channel == 'IB_OFF') {
					$input['ib_total_request'] = $tongSoLenh;
					$input['ib_total_receipt'] = $tongTienNhan;
					$input['ib_total_repayment'] =  $daTrich;
					$input['ib_total_refund'] = $daHoan;
					$input['ib_total_money_have'] =  $dangCo;
				}

				if ($channel == 'VIRTUALACCOUNT') {
					$input['va_total_request'] = $tongSoLenh;
					$input['va_total_receipt'] = $tongTienNhan;
					$input['va_total_repayment'] =  $daTrich;
					$input['va_total_refund'] = $daHoan;
					$input['va_total_money_have'] =  $dangCo;
				}

				if ($channel == 'WALLET') {
					$input['wallet_total_request'] = $tongSoLenh;
					$input['wallet_total_receipt'] = $tongTienNhan;
					$input['wallet_total_repayment'] =  $daTrich;
					$input['wallet_total_refund'] = $daHoan;
					$input['wallet_total_money_have'] =  $dangCo;
				}

				$this->__totalRequest += $tongSoLenh;
			}

			$input['total_request'] = $this->__totalRequest;
			$statisticPartner = CollectDebtStatisticPartner::query()->updateOrCreate([
				'contract_code' => $contractCode
			], $input);

			$statisticPartner->refresh();
		}

		return $this->genResult($statisticPartner);
	}

	public function genResult($statisticPartner)
	{
		$collectDebtSummary = CollectDebtSummary::query()
			->where('contract_code', $statisticPartner->contract_code)
			->select([
				'contract_code',
				'status_contract',
			])
			->first();

		$isPauseJobs = CollectDebtConfigAuto::isPauseContractJob($statisticPartner->contract_code);

		return [
			'channels' => [
				'MPOS' => [
					[
						"label" => "payment_method_code",
						"name" => "Kênh thu",
						"value" => "MPOS",
						"currency" => "VND"
					],
					[
						"label" => "so_lenh",
						"name" => "Số lệnh",
						"value" => $statisticPartner->mpos_total_request,
						"currency" => "VND"
					],
					[
						"label" => "tong_tien_nhan",
						"name" => "Tổng tiền nhận",
						"value" => $statisticPartner->mpos_total_receipt,
						"currency" => "VND"
					],
					[
						"label" => "da_trich",
						"name" => "Đã trích",
						"value" => $statisticPartner->mpos_total_repayment,
						"currency" => "VND"
					],
					[
						"label" => "da_hoan",
						"name" => "Đã hoàn",
						"value" => $statisticPartner->mpos_total_refund,
						"currency" => "VND"
					],
					[
						"label" => "dang_co",
						"name" => "Đang có",
						"value" => $statisticPartner->mpos_total_money_have,
						"currency" => "VND"
					],
				],

				'IB_OFF' => [
					[
						"label" => "payment_method_code",
						"name" => "Kênh thu",
						"value" => "IB_OFF",
						"currency" => "VND"
					],
					[
						"label" => "so_lenh",
						"name" => "Số lệnh",
						"value" => $statisticPartner->ib_total_request,
						"currency" => "VND"
					],
					[
						"label" => "tong_tien_nhan",
						"name" => "Tổng tiền nhận",
						"value" => $statisticPartner->ib_total_receipt,
						"currency" => "VND"
					],
					[
						"label" => "da_trich",
						"name" => "Đã trích",
						"value" => $statisticPartner->ib_total_repayment,
						"currency" => "VND"
					],
					[
						"label" => "da_hoan",
						"name" => "Đã hoàn",
						"value" => $statisticPartner->ib_total_refund,
						"currency" => "VND"
					],
					[
						"label" => "dang_co",
						"name" => "Đang có",
						"value" => $statisticPartner->ib_total_money_have,
						"currency" => "VND"
					],
				],

				'VIRTUALACCOUNT' => [
					[
						"label" => "payment_method_code",
						"name" => "Kênh thu",
						"value" => "VIRTUALACCOUNT",
						"currency" => "VND"
					],
					[
						"label" => "so_lenh",
						"name" => "Số lệnh",
						"value" => $statisticPartner->va_total_request,
						"currency" => "VND"
					],
					[
						"label" => "tong_tien_nhan",
						"name" => "Tổng tiền nhận",
						"value" => $statisticPartner->va_total_receipt,
						"currency" => "VND"
					],
					[
						"label" => "da_trich",
						"name" => "Đã trích",
						"value" => $statisticPartner->va_total_repayment,
						"currency" => "VND"
					],
					[
						"label" => "da_hoan",
						"name" => "Đã hoàn",
						"value" => $statisticPartner->va_total_refund,
						"currency" => "VND"
					],
					[
						"label" => "dang_co",
						"name" => "Đang có",
						"value" => $statisticPartner->va_total_money_have,
						"currency" => "VND"
					],
				],

				'WALLET' => [
					[
						"label" => "payment_method_code",
						"name" => "Kênh thu",
						"value" => "WALLET",
						"currency" => "VND"
					],
					[
						"label" => "so_lenh",
						"name" => "Số lệnh",
						"value" => $statisticPartner->wallet_total_request,
						"currency" => "VND"
					],
					[
						"label" => "tong_tien_nhan",
						"name" => "Tổng tiền nhận",
						"value" => $statisticPartner->wallet_total_receipt,
						"currency" => "VND"
					],
					[
						"label" => "da_trich",
						"name" => "Đã trích",
						"value" => $statisticPartner->wallet_total_repayment,
						"currency" => "VND"
					],
					[
						"label" => "da_hoan",
						"name" => "Đã hoàn",
						"value" => $statisticPartner->wallet_total_refund,
						"currency" => "VND"
					],
					[
						"label" => "dang_co",
						"name" => "Đang có",
						"value" => $statisticPartner->wallet_total_money_have,
						"currency" => "VND"
					],
				]
			],

			'configs' => [
				'MPOS' => [
					'TRICH_TAY' => $isPauseJobs ? 'YES' : 'NO',
				],
				'IB_OFF' => [
					'CHUYEN_KHOAN_NGOAI_BTN' => $collectDebtSummary->isHopDongDaTatToan() ? 'NO' : 'YES'
				],
				'WALLET' => [
					'QUAN_LY_VI_BTN' => 'YES'
				]
			],

			'request_statistic' => [
				'total_request' => $statisticPartner->total_request,
				'total_amount_success' => $statisticPartner->getTotalAmountSuccess(),
				'mpos_total_repayment' => $statisticPartner->mpos_total_repayment,
				'ib_total_repayment' => $statisticPartner->ib_total_repayment,
				'va_total_repayment' => $statisticPartner->va_total_repayment,
				'wallet_total_repayment' => $statisticPartner->wallet_total_repayment,
				'currency' => $statisticPartner->currency
			]
		];
	}
} // End class