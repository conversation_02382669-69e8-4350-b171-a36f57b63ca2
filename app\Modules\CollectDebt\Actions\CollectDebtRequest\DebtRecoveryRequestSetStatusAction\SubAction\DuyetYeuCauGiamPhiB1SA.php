<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSetStatusAction\SubAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAction\DebtRecoveryRequestActionCreateAction\DebtRecoveryRequestActionCreateAction;

class DuyetYeuCauGiamPhiB1SA
{
  public function run(CollectDebtRequest $collectDebtRequest, $request)
  {

    $collectDebtRequest->forceFill([
      'status' => CollectDebtEnum::REQUEST_STT_DA_DUYET,
      'approved_by' => $request->json('data.user_request_id'),
      'time_approved' => time(),
    ])->update();

    // Duyệt yêu cầu giảm phí bước 1
    $paramAction = [
      'type' => CollectDebtEnum::REQUEST_ACTION_TYPE_GIAM_PHI,
      'request_id' => $collectDebtRequest->id,
      'action_code' => CollectDebtEnum::REQUEST_ACTION_CODE_APPROVE1,
      'created_by' => $request->json('data.user_request_id'),
      'description' => $request->json('data.description') ?? "",
      'other_data' => '{}',
      'time_created' => time(),
    ];

    $action = app(DebtRecoveryRequestActionCreateAction::class)->run($paramAction);

    $rq = CollectDebtRequest::query()->find($collectDebtRequest->id);
    $rq->__apiMessage = 'Đã duyệt đề xuất giảm phí bước 1';

    return $rq;
  }
}