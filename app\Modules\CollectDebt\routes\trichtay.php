<?php 
use Illuminate\Support\Facades\Route;
use App\Modules\CollectDebt\Middleware\DoesntHaveAutoRequestTodayMiddleware;
use App\Modules\CollectDebt\Middleware\CheckDuplicateManualRequestTodayMiddleware;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtGuide\CollectDebtGuidePaymentController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtGuide\CollectDebtGuideStatisticController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtConfigAuto\CollectDebtConfigAutoController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest\CollectDebtRequestManualController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtPartner\CollectDebtPartnerStatisticController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtPartner\CollectDebtPartnerExternalTransferController;

// Thống kê "Thông tin thanh toán"
Route::any('/DebtRecoveryContractGuideGetPaymentStatistic', [
  'as' => 'DebtRecoveryContractGuideGetPaymentStatistic',
  'uses' => CollectDebtGuidePaymentController::class . '@getPaymentStatistic'
]);

// Thống kê "Thông tin trích nợ theo kênh"
Route::any('/DebtRecoveryPartnerStatisticByChannel', [
  'as' => 'DebtRecoveryPartnerStatisticByChannel',
  'uses' => CollectDebtPartnerStatisticController::class . '@statisticByChannel'
]);

// Thống kê theo channel (right pannel)
Route::any('/GetStatisticPartnerV2', [
  'as' => 'DebtRecoveryPartnerStatisticByChannel',
  'uses' => CollectDebtPartnerStatisticController::class . '@GetStatisticPartnerV2'
]);

// Tạo công nợ "Nhận chuyển khoản ngoài"
Route::any('/DebtRecoveryPartnerCreateExternalTransfer', [
  'as' => 'DebtRecoveryPartnerCreateExternalTransfer',
  'uses' => CollectDebtPartnerExternalTransferController::class . '@createExternalTransfer'
]);

// Dừng tạo yêu cầu thu tự động
Route::post('/DebtRecoveryPauseContractJob', [
  'as' => 'DebtRecoveryPauseContractJob',
  'uses' => CollectDebtConfigAutoController::class . '@pauseContractJob'
]);

// Tiếp tục yêu cầu tự động
Route::post('/DebtRecoveryResumeContractJob', [
  'as' => 'DebtRecoveryResumeContractJob',
  'uses' => CollectDebtConfigAutoController::class . '@resumeContractJob'
]);

// Danh sách yêu cầu đang thu tự động ngày hiện tại (rundate)
Route::any('/DebtRecoveryRequestListOfToday', [
  'as' => 'DebtRecoveryRequestListOfToday',
  'uses' => CollectDebtRequestManualController::class . '@listOfToday'
]);

// Hủy yêu cầu trích tự động
Route::post('/DebtRecoveryRequestCancelAutoType', [
  'as' => 'DebtRecoveryRequestCancelAutoTypeAction',
  'uses' => CollectDebtRequestManualController::class . '@cancelAutoType'
]);

// Kiểm tra yêu cầu trích
Route::post('/DebtRecoveryRequestCheck', [
  'as' => 'DebtRecoveryRequestCheck',
  'uses' => CollectDebtRequestManualController::class . '@checkAutoDebt'
]);

// Trích tay: Lấy ra các thông tin thống kê về dư nợ
Route::any('/DebtRecoveryContractGuideStatisticManual', [
  'as' => 'DebtRecoveryContractGuideStatisticManual',
  'uses' => CollectDebtGuideStatisticController::class . '@statisticManual'
]);

// Kiểm tra số dư VA, số dư MPOS
Route::any('/DebtRecoveryContractGuideCheckPartnerBalance', [
  'as' => 'DebtRecoveryContractGuideCheckPartnerBalance',
  'uses' => CollectDebtGuideStatisticController::class . '@checkPartnerBalance'
]);

// Tạo yêu cầu trích tay
Route::post('/DebtRecoveryRequestCreateManual', [
  'as' => 'DebtRecoveryRequestCreateManualAction',
  'uses' => CollectDebtRequestManualController::class . '@createManual'
])->middleware([
  DoesntHaveAutoRequestTodayMiddleware::class,
  CheckDuplicateManualRequestTodayMiddleware::class
]);

// Duyệt bước 2
Route::post('/DebtRecoveryRequestApprovedStep2', [
  'as' => 'DebtRecoveryRequestApprovedStep2',
  'uses' => CollectDebtRequestManualController::class . '@approveStep2'
]);

// Hủy yêu cầu rút tiền nhanh đã hạch toán
Route::post('/DebtRecoveryRequestCancelDebtNow', [
  'as' => 'DebtRecoveryRequestCancelDebtNowAction',
  'uses' => CollectDebtRequestManualController::class . '@cancelDebtNow'
]);

