<?php

namespace App\Http\Middleware;

use Closure;
use App\Lib\Logs;
use App\Utils\Result;
use App\Traits\ApiResponser;

class CheckTokenMiddleware {

    use ApiResponser;

    public  static $checksumBeforeMd5 = '';

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next) {
        
        if (env('APP_DEBUG_CHECK_SUM') == 0) {
            return $next($request);
        }

        $secretKey = env('CHECKSUMKEY');
        $token = $request->json('checksum');
        $tokenTime = $request->json('time_request');
        $Version = $request->json('version');
        $ChannelCode = $request->json('channel_code');
        $data = $request->json('data');
        
        $dataJson = json_encode($data, JSON_UNESCAPED_UNICODE);
        $dataJson = str_replace("null", '""', $dataJson);
        $checksumString = $dataJson . $Version . $ChannelCode . $tokenTime . $secretKey;
                
        self::$checksumBeforeMd5 = $checksumString;

				(new Logs())->writeFileLog($checksumString);
        
        $checksum = md5($checksumString);


        if (empty($token) || empty($tokenTime)) {
            return $this->errorResponse(Result::TOKEN_IS_NOT_FOUND, 'Token Không để rỗng ');
        }
        if ($checksum == $token) {
            return $next($request);
        } else {
            return $this->errorResponse(Result::TOKEN_IS_INVALID, 'Token Không hợp lệ');
        }
    }

    public function terminate($request, $response)
    {
       
    }
}
