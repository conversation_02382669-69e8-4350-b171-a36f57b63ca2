<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask;

use Exception;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLedger;

class CapNhatSoLieuVaoSoTask
{
	private bool $statusReportFee = false;
  /**
   * Không được update by model, vì có object tự thêm vào -> nó sẽ gây lỗi
   *
   */
  public function run(CollectDebtLedger $ledger, array $summaryData=[], array $paramUpdate=[], array $thuThua=[], array $canTru=[])
  {
    $summaryDataCollection = collect($summaryData);
    $otherData = $ledger->getLedgerOtherData();

    $summaryIndex = $ledger->getOtherDataTypeIndex('SUMMARY');
    $summaryOtherDataItem = $ledger->getOtherDataItem('SUMMARY');
    
    // tổng tiền thu được ghi vào sổ
    $summaryOtherDataItem['data']['total_amount_debit_success'] = $ledger->amount;

    // số tiền cấn trừ thành công cho lịch <ko quan tâm gốc, phí>
    $summaryOtherDataItem['data']['total_amount_paid'] = $summaryDataCollection->where('label', CollectDebtEnum::METADATA_CAN_TRU_TIEN_VAO_LICH)->sum('value');

    // số tiền thu thành công cho lịch thu gốc
    $summaryOtherDataItem['data']['amount_paid'] = $summaryDataCollection->where('label', CollectDebtEnum::METADATA_THU_GOC)->sum('value');

    // phí chậm kỳ thu được
    $summaryOtherDataItem['data']['fee_overdue_cycle_paid'] = $summaryDataCollection->where('label', CollectDebtEnum::METADATA_THANH_TOAN_PHI_CK)->sum('value');

    // phí quá hạn thu được
    $summaryOtherDataItem['data']['fee_overdue_paid'] = $summaryDataCollection->where('label', CollectDebtEnum::METADATA_THANH_TOAN_PHI_QH)->sum('value');

    // sinh phí chậm kỳ
    $summaryOtherDataItem['data']['fee_overdue_cycle'] = $summaryDataCollection->where('label', CollectDebtEnum::METADATA_PHI_CHAM_KY)->sum('value');
		if ($summaryOtherDataItem['data']['fee_overdue_cycle'] > 0) {
			$this->statusReportFee = true;
		}

    // sinh phí quá hạn
    $summaryOtherDataItem['data']['fee_overdue'] = $summaryDataCollection->where('label', CollectDebtEnum::METADATA_PHI_QUA_HAN)->sum('value');
		if ($summaryOtherDataItem['data']['fee_overdue'] > 0) {
			$this->statusReportFee = true;
		}

    // phí quá hạn được giảm
    $summaryOtherDataItem['data']['fee_overdue_reduction'] = $summaryDataCollection->where('label', CollectDebtEnum::METADATA_GIAM_PHI_QUA_HAN)->sum('value');

    // phí chậm kỳ được giảm
    $summaryOtherDataItem['data']['fee_overdue_cycle_reduction'] = $summaryDataCollection->where('label', CollectDebtEnum::METADATA_GIAM_PHI_CHAM_KY)->sum('value');

    // Xử lý thu thừa
    if (!empty($thuThua)) {
      $otherData[] = $thuThua;

      // ghi nhận số tiền thu thừa vào summary
      $summaryOtherDataItem['data']['total_amount_excess_revenue'] = collect($thuThua['data'])->sum('amount_excess');
    }

    $otherData[$summaryIndex] = $summaryOtherDataItem;

    // Xử lý cấn trừ
    if (!empty($canTru)) {
      $otherData[] = [
        'type' => 'OFFSET_DEBT',
        'note' => 'Thông tin cấn trừ nợ của lịch ghi trên sổ',
        'time_modified' => time(),
        'data' => $canTru
      ];
    }
    

    $paramUpdate['other_data'] =  json_encode($otherData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
    $paramUpdate['time_accounting'] = time();
		if ($this->statusReportFee) {
			$paramUpdate['status_gen_fee'] = CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT;
		}


    $updatedLedger = CollectDebtLedger::query()
																			->where('id', $ledger->id)
																			->where('status', CollectDebtEnum::LEDGER_STT_DANG_HACH_TOAN)
																			->update($paramUpdate);
    
		if (!$updatedLedger) {
			throw new Exception('Loi cap nhat so');
		}

    return $updatedLedger;
  }
}
