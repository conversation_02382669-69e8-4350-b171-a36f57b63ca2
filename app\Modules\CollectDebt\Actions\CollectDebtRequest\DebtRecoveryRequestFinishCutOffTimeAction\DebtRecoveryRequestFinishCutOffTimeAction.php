<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFinishCutOffTimeAction;

use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Http\Request;
use Mockery\CountValidator\Exact;
use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Enums\CacheEnum;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFinishCutOffTimeAction\SubAction\GetYeuCauCanXuLyCutOffSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFinishCutOffTimeAction\SubAction\XuLyYeuCauTrichQuaDoiTacSubAction;

class DebtRecoveryRequestFinishCutOffTimeAction
{
  public $log = [];

	private array $__yeuCauDaCutOff = [];

	public function initCutOff() {
		for ($i = 1; $i <= 30; $i++) {
			try {
				$result = $this->run();

				// Truy van ma he thong tra ra "EMPTY", thi break luon
				if ($result == 'EMPTY') {
					$this->__yeuCauDaCutOff[] = 'Khong co yc nao can cutoff';
					break;
				}

			} catch (\Throwable $th) {
				mylog(['Loi Cut Off' => Helper::traceError($th)]);
				// throw $th;
				@TelegramAlert::sendCutOff('Loi Xu Ly CutOff: ' . Helper::traceError($th));
				continue;
			} finally {
				usleep(300000);
			}
		}

		mylog(['cac ban ghi da cutoff' => $this->__yeuCauDaCutOff]);
		return $this->__yeuCauDaCutOff;
	}
  /**
   * Lưu ý, cronjob này đơn thuần chỉ là đánh dấu kết thúc yêu cầu, CHỨ KO HỀ TÍNH TOÁN TIỀN HAY TÍNH TOÁN LỊCH
   * Xử lý các lệnh tới hạn cut-off time gồm các bước sau 
   * 1. Truy vấn các yêu cầu đã expired
   * 2. Kiểm tra 1 lần cuối tình trạng lệnh trên hệ thống đối tác 
   *   + Với MPOS mà vẫn PENDING => thì hủy
   *   + Với kênh IB_OFF mà k có động tĩnh gì => đánh dấu thu 0đ (gọi vào API Create Partner <cân nhắc dùng UpdateOrCreate)
   * 
   * @return void
   */
  public function run()
  {
    $listYeuCauCutOffs = app(GetYeuCauCanXuLyCutOffSubAction::class)->run();
		
		if ($listYeuCauCutOffs->isEmpty()) {
			return 'EMPTY';
		}

		$collectDebtRequest = $listYeuCauCutOffs->first();

		mylog([
			'yeu cau thuc hien cutoff la' => $collectDebtRequest->partner_request_id
		]);

		// Update len dang xu ly
		$updatedDangXuLy = CollectDebtRequest::query()
																				 ->where('id', $collectDebtRequest->id)
																				 ->where('status_cutoff', CollectDebtEnum::REQUEST_CHUA_CUT_OFF)
																				 ->update(['status_cutoff' => CollectDebtEnum::REQUEST_DANG_CUT_OFF]);

		mylog([
			'ket qua cap nhat: ' => $updatedDangXuLy
		]);

		if (!$updatedDangXuLy) {
			throw new Exception('Khong the xu ly cut of cho yc: ' . $collectDebtRequest->partner_request_id);
		}

		$collectDebtRequest->refresh();

		if ($collectDebtRequest->status_cutoff != CollectDebtEnum::REQUEST_DANG_CUT_OFF) {
			mylog([
				'da update sang dang xu ly nhung fail' => $collectDebtRequest
			]);

			throw new Exception('Loi trang thai cut-off' . $collectDebtRequest->partner_request_id);
		}

    $collectDebtRequest->load(['collectDebtShare', 'collectDebtPartner']);

    DB::beginTransaction();
		try {
			// yc da cut off -> loi luon
			if ( $collectDebtRequest->isYeuCauCutOff() ) {
				mylog(['Yeu cau DA THUC HIEN cut off, tu choi xu ly' => $collectDebtRequest->partner_request_id]);
				$this->__updateVeDaXuLyCutOff($collectDebtRequest);

				$this->__yeuCauDaCutOff[] = $collectDebtRequest->id;
				DB::commit();
				
				return $collectDebtRequest;
			}

			// chua co partner -> can goi qua doi tac check lan cuoi
			if ( !$collectDebtRequest->collectDebtPartner || empty($collectDebtRequest->partner_transaction_id)) {
				mylog(['yeu cau khong co partner' => 1]);
				$coreContract = $collectDebtRequest->collectDebtShare->getCoreContractByGuide();
				$rq = app(XuLyYeuCauTrichQuaDoiTacSubAction::class)->run($collectDebtRequest, $coreContract);
				
				$this->__updateVeDaXuLyCutOff($collectDebtRequest);

				$this->__yeuCauDaCutOff[] = $collectDebtRequest->id;

				DB::commit();

				

				return $collectDebtRequest;
			}
			
			// Da co partner
			if ( $collectDebtRequest->collectDebtPartner ) {
				$this->__updateVeDaXuLyCutOff($collectDebtRequest);

				$this->__yeuCauDaCutOff[] = $collectDebtRequest->id;
				DB::commit();
				return $collectDebtRequest;
			}
		} catch (\Throwable $th) {
			$error = [
				'Loi cutoff' => traceErr($th), 
				'LogId' => request('api_request_id'),
			];
			mylog($error);
			
			$message = parseErr($error);

			@TelegramAlert::sendCutOff($message);

			DB::rollBack();

			CollectDebtRequest::query()
												->where('id', $collectDebtRequest->id)
												->where('status_cutoff', CollectDebtEnum::REQUEST_DANG_CUT_OFF)
												->update(['status_cutoff' => CollectDebtEnum::REQUEST_CHUA_CUT_OFF]);

			throw new Exception($message);
		}
  }

	private function __updateVeDaXuLyCutOff(CollectDebtRequest $collectDebtRequest) {
		$updated =CollectDebtRequest::query()->where('id', $collectDebtRequest->id)
															 					 ->update([
																					'status_cutoff' => CollectDebtEnum::REQUEST_DA_XL_CUT_OFF
																				]);
		if (!$updated) {
			throw new Exception('khong the cap nhat ve trang thai da cut off');
		}

		return $updated;
	}
} // End class