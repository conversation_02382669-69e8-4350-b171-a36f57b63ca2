<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFinishCutOffTimeAction\SubAction;

use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;

class DanhDauKetThucYeuCauSubAction
{
  public function run(CollectDebtRequest $collectDebtRequest, $isHuyThanhCong=false)
  {
		// Chưa về trạng thái cuối thì mới sét là CẦN KIỂM TRA
		if (!$collectDebtRequest->isRequestFinalStatus()) {
			$collectDebtRequest->status = CollectDebtEnum::REQUEST_STT_CAN_HOAN_THANH_VA_KIEM_TRA;
		}
    
    
    if ( empty($collectDebtRequest->partner_transaction_id) ) {
			mylog(['vao day' => 'fake ket qua la da nhan']);
      $collectDebtRequest->status = CollectDebtEnum::REQUEST_STT_DA_DUYET;
			$collectDebtRequest->status_payment = CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA;
    }
    
    $collectDebtRequest->time_receivered = time();
		if ($isHuyThanhCong) {
			$collectDebtRequest->time_canceled = time();
			$collectDebtRequest->canceled_by = Helper::getCronJobUser();
		}


    $collectDebtRequest->save();

    return $collectDebtRequest;
  }
} // End class
