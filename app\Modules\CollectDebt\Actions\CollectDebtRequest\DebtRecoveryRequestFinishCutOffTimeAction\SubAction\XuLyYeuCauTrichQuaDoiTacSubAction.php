<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFinishCutOffTimeAction\SubAction;

use Exception;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtCoreContract;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\CheckRequestViaMposSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCancelPaymentAction\DebtRecoveryRequestCancelPaymentAction;

class XuLyYeuCauTrichQuaDoiTacSubAction
{
	private $__isHuyYeuCauThanhCong = false;

  public function run(CollectDebtRequest $collectDebtRequest, CollectDebtCoreContract $coreContract): CollectDebtRequest
  {
    $request = request();

    if (empty($collectDebtRequest->partner_transaction_id) && $collectDebtRequest->isPaymentViaChannelMpos()) {
      $rq = app(DanhDauKetThucYeuCauSubAction::class)->run($collectDebtRequest);
      return $rq;
    }

    // Yêu cầu chưa hề đc gửi qua MPOS thì kết thúc luôn
    if ($collectDebtRequest->isUnsentPayment()) {
      $partner = app(CreatePartnerKhongDongSubAction::class)->run($collectDebtRequest, $coreContract);
      $rq = app(DanhDauKetThucYeuCauSubAction::class)->run($collectDebtRequest);
      return $rq;
    }

    // Kết quả còn chưa rõ ràng thì gọi check MPOS
    if ( !empty($collectDebtRequest->partner_transaction_id) ) {
      $checkDebtResult = app(CheckRequestViaMposSubAction::class)->run($collectDebtRequest, $request);
			
			$requestDebitStatus = $checkDebtResult['data']['status'] ?? '';

			// khong co thong tin hoac het qua khac APPROVED
			if ( empty($requestDebitStatus) || !in_array($requestDebitStatus, ['APPROVED', 'SUCCESS']) ) {
				mylog([
					'yeu cau chua trich duoc tien' => 'ok',
					'thuc hien goi huy ngay lap tuc' => 'ok'
				]);

				// goi cancel sang ben mpos tranh rui ro thu thua
				$r = @app(DebtRecoveryRequestCancelPaymentAction::class)->run($collectDebtRequest);

				// huy thanh cong
				if (
					!empty($r['data']['error_code']) && 
					( $r['data']['error_code'] == '00' || $r['data']['error_code'] == '-1' )
				) {
					$this->__isHuyYeuCauThanhCong = true;
				}

				mylog([
					'cutoff 22h' => 'ok',
					'goi huy lenh trich sang mpos, ma chung tu: ' => $collectDebtRequest->partner_transaction_id,
					'ket qua huy lenh trich' => $r
				]);
				
				$partner = app(CreatePartnerKhongDongSubAction::class)->run($collectDebtRequest, $coreContract);
				$rq = app(DanhDauKetThucYeuCauSubAction::class)->run($collectDebtRequest, $this->__isHuyYeuCauThanhCong);
				return $rq;
			}
			// co thong tin trich duoc tien
			elseif ($requestDebitStatus == 'APPROVED' || $requestDebitStatus == 'SUCCESS') {
				// doan nay la trich duoc tien
				if ( !$collectDebtRequest->isYeuCauCutOff() ) {
					try {
						$otherData = $collectDebtRequest->getRequestOtherData();

						$otherDataJson = json_encode($otherData, JSON_UNESCAPED_UNICODE);

						CollectDebtRequest::query()->where('id', $collectDebtRequest->id)->update([
							'other_data' => $otherDataJson
						]);
					}catch(\Throwable $th) {
						$errorMessgae = parseErr([
							'Error' => 'Loi cap nhat CUTOFF cho lenh trich duoc tien',
							'ContractCode' => $collectDebtRequest->contract_code,
							'Msg' => $th->getMessage()
						]);
						TelegramAlert::sendMessage($errorMessgae);
					}
				}

				return $collectDebtRequest;
			}

			// khong biet la loi gi
			else {
				mylog([
					'yeu cau chua trich duoc tien' => 'ok',
					'thuc hien goi huy ngay lap tuc' => 'ok'
				]);

				// goi cancel sang ben mpos tranh rui ro thu thua
				$r = @app(DebtRecoveryRequestCancelPaymentAction::class)->run($collectDebtRequest);
				mylog([
					'cutoff 22h' => 'ok',
					'goi huy lenh trich sang mpos, ma chung tu: ' => $collectDebtRequest->partner_transaction_id,
					'ket qua huy lenh trich' => $r
				]);
				
				$partner = app(CreatePartnerKhongDongSubAction::class)->run($collectDebtRequest, $coreContract);
				$rq = app(DanhDauKetThucYeuCauSubAction::class)->run($collectDebtRequest);
				return $rq;
			}
		} // End if check empty partner_transaction_id
  }
} // End class