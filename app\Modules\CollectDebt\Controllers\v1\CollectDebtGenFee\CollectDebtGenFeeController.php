<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtGenFee;

use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Model\CollectDebtGenFee;
use App\Modules\CollectDebt\Resources\CollectDebtGenFeeResourceCollection;

class CollectDebtGenFeeController extends Controller
{
	public function searchData(Request $request)
	{
		try {
			$contractCode = trim($request->json('data.contract_code', ''));

			$collectDebtGenFee = CollectDebtGenFee::query()->with('collectDebtShareOnly:contract_code,profile_data');

			if (!empty($contractCode)) {
				$collectDebtGenFee->where('contract_code', $contractCode);
			}

			$collectDebtGenFee = $collectDebtGenFee->orderBy('id', 'DESC')->paginate(
				$request->json('data.limit', 20),
				['*'],
				'page',
				$request->json('data.page', 1)
			);

			$resource = new CollectDebtGenFeeResourceCollection($collectDebtGenFee);
			$response = $resource->toResponse($request)->getData(true);
			unset($response['links']);
			return $this->successResponse([
				'filters' => [
					'fee_type' => [
						'1' => 'Phí chậm kỳ',
						'2' => 'Phí quá hạn',
						'3' => 'Phí phạt trả chậm 3%'
					],
					'status' => [
						'1' => '--'
					]
				],
				'rows' => $response
			], $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
