<?php

namespace App\Utils;

use App\Lib\Logs;
use App\Utils\ApiConnect\ApiCashIn;
use GuzzleHttp\Client;
use Illuminate\Support\Str;
use App\Utils\ApiConnect\ApiProfile;
use App\Utils\ApiConnect\ApiScoring;
use App\Utils\ApiConnect\ApiCategory;
use App\Utils\ApiConnect\ApiCustomer;
use App\Utils\ApiConnect\ApiRequestDebt;
use GuzzleHttp\Exception\RequestException;
use App\Utils\ApiConnect\ApiPartnerGateway;
use App\Utils\ApiConnect\ApiMerchantProfile;

class ApiConnect
{
    protected $_log = [];
    protected $_apiUrl;
    protected $_time_request;
    protected $_version;
    protected $_channel_code;
    protected $_secret_key;
    protected $_checksum;

    public $module = [];
    public $uniqueKey = '';

    public function __construct($module, $path) {
        $this->uniqueKey = sprintf('%s--%s--%s', $module, $path, Str::random(48));
        $this->module[$this->uniqueKey]['module'] = $module;
        $this->module[$this->uniqueKey]['path'] = $path;

        switch ($module) {
            case CommonVar::API_CATEGORY_MODULE:
                $config = new ApiCategory();
                break;

            case CommonVar::API_MERCHANT_PROFILE_MODULE:
                $config = new ApiMerchantProfile();
                break;

            case CommonVar::API_PARTNER_GATEWAY_MODULE:
                $config = new ApiPartnerGateway();
                break;

            case CommonVar::API_PROFILE_MODULE:
                $config = new ApiProfile();
                break;

            case CommonVar::API_SCORING_MODULE:
                $config = new ApiScoring();
                break;

            case CommonVar::API_CUSTOMER_MODULE:
                $config = new ApiCustomer();
                break;

            case CommonVar::API_REQUEST_DEBT_MODULE:
                $config = new ApiRequestDebt();
                break;

						case CommonVar::API_CASH_IN_MODULE:
							$config = new ApiCashIn();
							break;

            default:
                $config = null;
                break;
        }
        
        if (!empty($config)) {
            $this->_apiUrl = $config->_apiUrl . $path;
            $this->_channel_code = $config->_channel_code;
            $this->_version = $config->_version;
            $this->_secret_key = $config->_secret_key;
        }
        $this->_time_request = time();
    }

    public function callApi($param, $method = 'POST') {
        $this->module[$this->uniqueKey]['params'] = $param;

        $client = new Client([
            'headers' => ['Content-Type' => 'application/json']
        ]);
        try {
            $this->_checksum = $this->_createCheckSum($param);
            $response = $client->request($method, $this->_apiUrl, [
                    'body' => json_encode(
                        [
                            'data' => $param,
                            'checksum' => $this->_checksum,
                            'channel_code' => $this->_channel_code,
                            'time_request' => $this->_time_request,
                            'version' => $this->_version,
                            'api_request_id' => request()->json('api_request_id')
                        ]
                    )
                ]
            );
            
            $result = $response->getBody()->getContents();
            $this->module[$this->uniqueKey]['api_result_success'] = json_decode($result, true);
        } catch (\Throwable $e) {
            $this->module[$this->uniqueKey]['api_response_error'] = [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'message' => $e->getMessage()
            ];
            $result = false;
        } finally {
            return $result;
        }
    }


    protected function _createCheckSum($params) {
        $dataJson = json_encode($params, JSON_UNESCAPED_UNICODE);
        $data = str_replace("null", '""', $dataJson);
        $this->module[$this->uniqueKey]['request_json_encode'] = $dataJson;

        $stringChecksum = $data . $this->_version . $this->_channel_code . $this->_time_request . $this->_secret_key;
        $this->module[$this->uniqueKey]['request_checksum'] = $stringChecksum;

        $this->_checksum = md5($stringChecksum);
        $this->module[$this->uniqueKey]['request_checksum_md5'] = $this->_checksum ;

        return $this->_checksum;
    }
}