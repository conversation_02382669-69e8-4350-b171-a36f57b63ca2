<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction;

use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtProcessing;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;

class AlertStuckRequestAction extends BatchProcessingAction
{
	public function run()
	{
		$startTime = now()->setTime(22, 30);
		$endTime = now()->setTime(23, 59);

		if (now()->between($startTime, $endTime)) {
			return ['msg' => 'Current range time is not allowed'];
		}

		$listRequestStuck = CollectDebtProcessing::query()
			->where('expired_at', '<=', now())
			->select(['id', 'contract_code', 'partner_request_id', 'expired_at'])
			->get();

		if ($listRequestStuck->isEmpty()) {
			return ['msg' => 'Everything is processed'];
		}

		$listRequestStuck->load([
			'collectDebtRequestOnly:id,contract_code,partner_request_id',
			'collectDebtRequestOnly.collectDebtLedger:id,request_id,contract_code,status',
		]);

		// Xử lý giải phóng lệnh trích
		$listRequestStuck->each(function (CollectDebtProcessing $p) {
			$collectDebtLedger = $p->collectDebtRequestOnly->collectDebtLedger;
			
			if (!$collectDebtLedger) {
				$msg = sprintf("env: %s --- Lệnh trích $p->partner_request_id chưa có sổ!", config('app.env'));
				@TelegramAlert::alertLenhTrichBiTreo($msg);
				return;
			}
			

			if ($collectDebtLedger && $collectDebtLedger->status == CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN) {
				$p->delete();
				return $p->partner_request_id;
			}

			$msg = sprintf("env: %s --- Lệnh trích $p->partner_request_id không thể giải phóng. Kiểm tra ngay!", config('app.env'));
			@TelegramAlert::alertLenhTrichBiTreo($msg);
		});

		return ['msg' => 'handle done'];
	}
}  // End class