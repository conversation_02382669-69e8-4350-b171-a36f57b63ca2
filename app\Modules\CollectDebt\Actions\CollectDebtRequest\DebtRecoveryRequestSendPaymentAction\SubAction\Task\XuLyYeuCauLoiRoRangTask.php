<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction\Task;

use App\Modules\CollectDebt\Model\CollectDebtRequest;

class XuLyYeuCauLoiRoRangTask
{
	/**
	 * Bối cảnh: YC thực hiện gửi và bị lỗi rõ ràng -> đánh dấu time_sended và sẽ gọi lại sau 20p
	 * B1: Gửi lại qua MPOS
	 * B2: Trả về kết quả là trùng
	 *
	 * @param CollectDebtRequest $collectDebtRequest
	 * @return CollectDebtRequest
	 */
	public function run(CollectDebtRequest $collectDebtRequest, $sendDebtResult): CollectDebtRequest
	{
		$collectDebtRequest->time_sended = time();
		$collectDebtRequest->save();
		return $collectDebtRequest;
	}
} // End class