<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction;

use Exception;
use App\Lib\Helper;
use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerCreateRequest;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerCreateAction\SubAction\GhiSoChoYeuCauDaCoSoSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction\TaoYeuCauKhongCanGuiDoiTacSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction\CapNhatYeuCauVeTrangThaiCuoiSubAction;

class PartnerCheckBatchAction extends BatchProcessingAction
{
	public function run()
	{
		$startTime = microtime(true);

		$client = $this->createHttpClient();

		CollectDebtPartner::query()
			->join('debt_recovery_summary', 'debt_recovery_summary.contract_code', '=', 'debt_recovery_partner.contract_code')
			->whereRaw("LENGTH(debt_recovery_partner.contract_code) > 0")
			->where('debt_recovery_partner.status', CollectDebtEnum::PARTNER_STT_CHUA_XU_LY)
			->where('debt_recovery_partner.status_refund', CollectDebtEnum::PARTNER_STT_RF_CHUA_HOAN)
			->where(function ($query) {
					$query->where('payment_method_code', 'MPOS')
								->orWhere(function ($sub1) {
									$sub1->whereIn('payment_method_code', ['IB_OFF', 'WALLET', 'VIRTUALACCOUNT'])
											 ->whereAvailableBalance();
								});
			})->orderByRaw('debt_recovery_partner.number_perform ASC, debt_recovery_partner.id ASC')
				->selectRaw('debt_recovery_partner.*')
				->chunkById(self::CHUNK_LIMIT, function (Collection $listPartner) use ($client, $startTime) {
					if ((microtime(true) - $startTime) > self::BATCH_LIMIT_IN_SECONDS) {
						$this->__errorIds[] = "Times up";
						return false;
					}

				
					$this->processPartnerBatch($client, $listPartner);

					unset($listPartner);

					usleep(5000);
				}, 'debt_recovery_partner.id');

		return [
			'error_ids' => $this->__errorIds
		];
	}

	
	private function processPartnerBatch(Client $client, Collection $listPartner): void
	{
		// Generator
		$requests = function () use ($listPartner) {
			foreach ($listPartner as $collectDebtPartner) {
				$url = config('app.url') . '/HandlePartnerCheck/' . $collectDebtPartner->id;
				yield $collectDebtPartner->id => new Request('POST', $url, ['Content-Type' => 'application/json']);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => self::POOL_CONCURRENCY,
			'fulfilled' => function ($response, $id) {
				// $body = (string)$response->getBody();
			},
			'rejected' => function (\Throwable $reason, string $id) {
				$msg = "[PartnerCheck --->$id] failed: " . $reason->getMessage();
				Log::info($msg);
				$this->__errorIds[] = $id;
			},
		]);

		$promise = $pool->promise();
		$promise->wait();

		unset($pool, $promise);
	}

	public function HandlePartnerCheck($id)
	{
		$collectDebtPartner = CollectDebtPartner::query()->find($id);

		if (!$collectDebtPartner) {
			return 'Partner NotFound';
		}

		if ($collectDebtPartner->status != CollectDebtEnum::PARTNER_STT_CHUA_XU_LY) {
			return 'Partner is processing...';
		}

		// Update lên thành đang xử lý
		$wasUpdateProcessing = CollectDebtPartner::query()
			->where('id', $collectDebtPartner->id)
			->where('status', CollectDebtEnum::PARTNER_STT_CHUA_XU_LY)
			->update([
				'status' => CollectDebtEnum::PARTNER_STT_DANG_XU_LY,
				'number_perform' => 1
			]);

		if ( !$wasUpdateProcessing ) {
			throw new Exception('Loi khong the update partner thanh PROCESSING...');
		}

		DB::beginTransaction();
		
		try {
			if ($collectDebtPartner->isTonTaiYeuCau()) {
				mylog([
					'Công nợ đã có yêu cầu' => 'YES',
					'Cong no dang xu ly' => $collectDebtPartner->partner_request_id
				]);

				$collectDebtPartnerCreditRequest = app(CapNhatYeuCauVeTrangThaiCuoiSubAction::class)->run($collectDebtPartner);

				/**
				 * Xuống đến đây mà partner vẫn còn `ĐANG XỬ LÝ`, chứng tỏ bị lỗi gì đó 
				 * -> UPDATE lại thành CHƯA XỬ LÝ và break;
				 */
				if ($collectDebtPartnerCreditRequest['partner']->isPartnerDangXuLy()) {
					mylog(['Da bi loi gi do ma khong the cap nhat ve trang thai cuoi' => 'ok']);
					throw new Exception('Loi khong cap nhat duoc yeu cau ve trang thai cuoi');
				}

				// Nếu yc đã ghi sổ rồi và ghi nhận số tiền thừa, bắt !empty để lịch thu quá khứ ko bị ghi sổ 2 lần
				$collectDebtRequest = $collectDebtPartner->collectDebtRequest;
				if (!empty($collectDebtPartnerCreditRequest['is_excess']) && !empty($collectDebtRequest->partner_transaction_id)) {
					$inputs = [
						'data' => [
							'profile_id'    => $collectDebtRequest->profile_id,
							'contract_code' => $collectDebtRequest->contract_code,
							'plan_ids'      => $collectDebtRequest->plan_ids,
							'request_id'    => $collectDebtRequest->id,
							'currency'      => $collectDebtRequest->currency,
							'amount'        => $collectDebtPartner->amount_receiver,
							'description'   => 'TRICH_MUON',
							'status'        => CollectDebtEnum::LEDGER_STT_CHUA_XU_LY,
							'time_record'   => time(),
							'created_by'    => Helper::getCronJobUser(),
							'time_created'  => time(),
						]
					];

					$rq = new DebtRecoveryLedgerCreateRequest();
					$rq->setJson(new \Symfony\Component\HttpFoundation\ParameterBag((array) $inputs));

					app(GhiSoChoYeuCauDaCoSoSubAction::class)->run($collectDebtRequest, $rq, $collectDebtPartner);
				}

				DB::commit();


				return $id;
			} // End if is ton tai yeu cau

			if ($collectDebtPartner->isKhongTonTaiYeuCau()) {
				mylog([
					'Cong no KHONG CO yêu cầu' => 'YES',
					'Cong no dang xu ly, ma chung tu la:' => $collectDebtPartner->partner_transaction_id
				]);

				$collectDebtPartnerResult = app(TaoYeuCauKhongCanGuiDoiTacSubAction::class)->run($collectDebtPartner);
				DB::commit();

				return $id;
			}

			throw new Exception('Không biết là lỗi gì...');
		} catch (\Throwable $th) {
			DB::rollBack();

			// Khi ban ghi bi loi, thi can tra lai trang ban dau (CHUA XU LY)
			$updateChuaXuLy = CollectDebtPartner::query()->where('id', $collectDebtPartner->id)
																 ->where('status', CollectDebtEnum::PARTNER_STT_DANG_XU_LY)
																 ->update([
																	'status' => CollectDebtEnum::PARTNER_STT_CHUA_XU_LY,
																	'number_perform' => 0,
																 ]);

			if (!$updateChuaXuLy) {
				mylog(['[LOI UPDATE VE CHUA XU LY]' => $updateChuaXuLy]);
			}

			throw new Exception('Loi xu ly partner: ' . Helper::traceError($th));
		}

		return $id;
	}
}  // End class