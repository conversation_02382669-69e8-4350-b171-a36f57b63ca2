<?php

namespace App\Modules\CollectDebt\Model;

use Illuminate\Database\Eloquent\Model;

class CollectDebtPlan extends Model
{
	protected $table 	 = 'debt_recovery_contract_plan';
	public $timestamps = false;
	protected $guarded = [];

	public function collectDebtProcessing() {
		return $this->hasOne(CollectDebtProcessing::class, 'contract_code', 'contract_code');
	}

	public function collectDebtShareOnly() {
		return $this->belongsTo(CollectDebtShareOnly::class, 'contract_code', 'contract_code');
	}
} // End class
