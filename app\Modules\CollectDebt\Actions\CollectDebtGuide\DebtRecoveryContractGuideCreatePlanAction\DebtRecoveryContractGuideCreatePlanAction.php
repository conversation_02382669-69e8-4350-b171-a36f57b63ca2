<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideCreatePlanAction;

use Exception;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Ultilities\ReminderUtil;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateAction\SubAction\CapNhatMasterIdSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateAction\DebtRecoveryContractPlanCreateAction;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideCreatePlanAction\SubAction\KhoiTaoBangTongHopSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideCreatePlanAction\SubAction\UpdateGuideByModelSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideCreatePlanAction\SubAction\BuildDailyCollectScheduleSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideCreatePlanAction\SubAction\BuildPeriodCollectDebtScheduleSubAction;

class DebtRecoveryContractGuideCreatePlanAction
{  
  public $log = [];
  /**
   * CRON JOB: Tạo lịch thu hồi từ chỉ dẫn
   * @param CollectDebtGuide [Danh sách chỉ dẫn cần được tạo lịch]
   * @param Collection $collectDebtGuideCollection <CollectDebtGuide::class>
   *
   * @return void
   */
  public function run(Collection $collectDebtGuideCollection)
  {
    throw_if($collectDebtGuideCollection->isEmpty(), new Exception('Không có thông tin chỉ dẫn cần tạo lịch'));
    
    foreach ($collectDebtGuideCollection as $collectDebtGuide) {
      $this->log[$collectDebtGuide->contract_code]['Tạo lịch thu từ chỉ dẫn có id là:'] = $collectDebtGuide->id;

      // Loại HĐ trích nợ theo ngày
      if ($collectDebtGuide->isChiDanHopDongTrichNgay()) {
        $this->log[$collectDebtGuide->contract_code]['Là hợp đồng trích nợ theo ngày'] = $collectDebtGuide->toJson();
        $scheduleCreateParams = $this->handleCreateDailySchedule($collectDebtGuide);
      }
      
      // Loại HĐ Trích nợ theo chu kỳ
      if ($collectDebtGuide->isChiDanHopDongTrichKy()) {
        $this->log[$collectDebtGuide->contract_code]['Là hợp đồng trích nợ theo chu kỳ'] = $collectDebtGuide->toJson();
        $scheduleCreateParams = $this->handleCreatePeriodSchedule($collectDebtGuide);
      }

      $this->log[$collectDebtGuide->contract_code]['Param tạo lịch là'] = $scheduleCreateParams;

      $scheduleInsertResult = app(DebtRecoveryContractPlanCreateAction::class)->run($scheduleCreateParams);
      
      $this->log[$collectDebtGuide->contract_code]['Kết quả tạo lịch là'] = $scheduleCreateParams;

      // Cập nhật lại chỉ dẫn la đã tạo lịch thành công hay chưa
      if ( $scheduleInsertResult['create_schedule_result_success'] ) {
        app(UpdateGuideByModelSubAction::class)->run($collectDebtGuide, ['status' => CollectDebtEnum::GUIDE_STT_DA_TAO_LICH_THANH_CONG]);
        app(KhoiTaoBangTongHopSubAction::class)->run($collectDebtGuide, $scheduleInsertResult['collect_debt_schedule']);
        app(CapNhatMasterIdSubAction::class)->run($scheduleInsertResult['collect_debt_schedule']);

				ReminderUtil::createReminder($collectDebtGuide, $scheduleInsertResult['collect_debt_schedule']);
      }

      if ( !$scheduleInsertResult['create_schedule_result_success'] ) {
        app(UpdateGuideByModelSubAction::class)->run($collectDebtGuide, ['status' => CollectDebtEnum::GUIDE_STT_DANG_TAO_LICH]);
      }

      mylog($this->log);
      return $scheduleInsertResult['collect_debt_schedule'];
    }
  }

  public function handleCreateDailySchedule(CollectDebtGuide $collectDebtGuide) {
    $schedulesInfo = app(BuildDailyCollectScheduleSubAction::class)->run($collectDebtGuide);

    throw_if( empty($schedulesInfo), new Exception('Lỗi không tạo được lịch thu'));
    
    $collectDebtScheduleInsertParams = [];

    foreach ($schedulesInfo as $index => $scheduleItem) {
      $debitCycleDate = $scheduleItem['collect_debt_schedule']; // Ngày chu kỳ trích nợ

      $collectDebtScheduleInsertParams[] = [
        'profile_id'           => $collectDebtGuide->profile_id,
        'contract_code'        => $collectDebtGuide->contract_code,
        'contract_type'        => $collectDebtGuide->contract_type,
        'type'                 => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_CHINH,
        'isfee'                => CollectDebtEnum::SCHEDULE_LA_LICH_THU_NO_GOC,
        'debit_begin'          => $scheduleItem['debit_begin'],
        'debit_end'            => $scheduleItem['debit_end'],
        'time_start'           => $debitCycleDate->copy()->startOfDay()->timestamp,
        'time_end'             => $debitCycleDate->copy()->endOfDay()->timestamp,
        'rundate'              => $collectDebtGuide->isHopDongNextlend() ? $debitCycleDate->copy()->addDay()->setHours(5)->format('Ymd')
																																				 : $debitCycleDate->copy()->format('Ymd'),
        'amount_period_debit'  => $scheduleItem['amount_to_be_paid'],
        'request_amount_debit' => $scheduleItem['amount_to_be_paid'],
        'success_amount_debit' => 0,
        'other_data'           => $collectDebtGuide->other_data,
        'description'          => $collectDebtGuide->description ?? '',
        'created_by'           => $collectDebtGuide->created_by,
        'time_created'         => time(),
        'is_settlement'        => $scheduleItem['is_settlement'],
        'status'               => CollectDebtEnum::SCHEDULE_STT_MOI,
        'cycle_number'        =>  $scheduleItem['cycle_number'],
        'other_data'          => $scheduleItem['other_data'],
      ];
    }

    return $collectDebtScheduleInsertParams;
  }

  public function handleCreatePeriodSchedule(CollectDebtGuide $collectDebtGuide) {
    $schedulesInfo = app(BuildPeriodCollectDebtScheduleSubAction::class)->run($collectDebtGuide);

    throw_if( empty($schedulesInfo), new Exception('Lỗi không tạo được lịch thu'));
    
    $collectDebtScheduleInsertParams = [];
    foreach ($schedulesInfo as $index => $scheduleItem) {
      $collectDebtScheduleInsertParams[] = [
        'profile_id'           => $collectDebtGuide->profile_id,
        'contract_code'        => $collectDebtGuide->contract_code,
        'contract_type'        => $collectDebtGuide->contract_type,
        'type'                 => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_CHINH,
        'isfee'                => CollectDebtEnum::SCHEDULE_LA_LICH_THU_NO_GOC,
        'debit_begin'          => $scheduleItem['debit_begin'],
        'debit_end'            => $scheduleItem['debit_end'],
        'time_start'           => $scheduleItem['time_start'],
        'time_end'             => $scheduleItem['time_end'],
        'rundate'              => $scheduleItem['rundate'],
        'amount_period_debit'  => $scheduleItem['amount_to_be_paid'],
        'request_amount_debit' => $scheduleItem['amount_to_be_paid'],
        'success_amount_debit' => 0,
        'other_data'           => $collectDebtGuide->other_data,
        'description'          => $collectDebtGuide->description ?? '',
        'created_by'           => $collectDebtGuide->created_by,
        'time_created'         => time(),
        'is_settlement'        => $scheduleItem['is_settlement'],
        'status'               => CollectDebtEnum::SCHEDULE_STT_MOI,
        'cycle_number'         => $scheduleItem['cycle_number'],
        'other_data'           => $scheduleItem['other_data'],
      ];
    }

    return $collectDebtScheduleInsertParams;
  }
} // End class