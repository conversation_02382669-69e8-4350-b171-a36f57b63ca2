<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerStatisticByChannelAction;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerStatisticByChannelAction\SubAction\GetPartnersCuaHopDongSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractCreateRequestViaWalletAction\SubAction\GetTaiKhoanThanhToanViSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerStatisticByChannelAction\SubAction\GetThongKeLenhTrichTheoHopDongSubAction;

class DebtRecoveryPartnerStatisticByChannelAction
{
  public array $returnData = [];

  public function run(Request $request): array
  {
    $contractCode = $request->json('data.contract_code');

    $collectDebtShare = CollectDebtShare::where('contract_code', $contractCode)
																				->select([
																					'id',
																					'contract_code',
																					'payment_guide',
																					'profile_data'
																				])
																				->first();
    throw_if(!$collectDebtShare, new Exception('Không tìm thấy chỉ dẫn'));

    $partners = app(GetPartnersCuaHopDongSubAction::class)->run($contractCode);

    $thongKeLenhTrich = app(GetThongKeLenhTrichTheoHopDongSubAction::class)->run($collectDebtShare->contract_code);

    $this->setDefaultPartner($collectDebtShare, $thongKeLenhTrich);

    if ($partners->isEmpty()) {
      return $this->returnData;
    }

    $partnersGroupByChannel = $partners->groupBy('payment_method_code');
		
    $partnersGroupByChannel->map(function (Collection $groupPartnersByChanel, string $channel) use ($collectDebtShare) {
      $tongTienNhan = 0;
      $daTrich = 0;
      $dangCo = 0;
      $daHoan = 0;

      $groupPartnersByChanel->map(function (CollectDebtPartner $partner) use (&$tongTienNhan, &$daTrich, &$dangCo, &$daHoan) {
        $tongTienNhan += $partner->isPartnerKhongLoiHoacTuChoi() ? $partner->amount_receiver : 0;
        $daTrich += $partner->amount_payment;
        $dangCo += $partner->isPartnerChuaXuLy() ? $partner->getAmountBalance() : 0;
        $daHoan += $partner->amount_refund;
      });

      $daTrich = min($daTrich, $tongTienNhan);

			// $currency =  $collectDebtShare->getCurrencyShared();
			$currency =  'VND';

			$getSoDuDangCo = function () use ($channel, $collectDebtShare, $dangCo) {
				if ($channel == 'MPOS') {
					return 0;
				}

				return $dangCo;
			};

			$mergeData = [
				[
					'label' => 'tong_tien_nhan',
					'name' => 'Tổng tiền nhận',
					'value' => $tongTienNhan,
					'currency' => $currency,
				],

				[
					'label' => 'da_trich',
					'name' => 'Đã trích',
					'value' => $daTrich,
					'currency' => $currency,
				],

				[
					'label' => 'da_hoan',
					'name' => 'Đã hoàn',
					'value' => $daHoan,
					'currency' => $currency,
				]
			];

			if ($channel != 'WALLET') {
				$mergeData[] = [
					'label' => 'dang_co',
					'name' => 'Đang có',
					'value' => $getSoDuDangCo($channel, $collectDebtShare, $dangCo), 
					'currency' => $currency,
				];
			}

      $this->returnData[$channel] = array_merge($this->returnData[$channel], $mergeData);
    });

    return $this->returnData;
  }

  public function setDefaultPartner(CollectDebtShare $collectDebtShare, array $thongKeLenhTrich)
  {
    $paymentGuides = $collectDebtShare->getPaymentGuide();
		$currency =  'VND';

    foreach ($paymentGuides as $pg) {
      $this->returnData[$pg['payment_method_code']] = [
        [
          'label' => 'payment_method_code',
          'name' => 'Kênh thu',
          'value' => $pg['payment_method_code'],
          'currency' => $currency,
        ],

        [
          'label' => 'so_lenh',
          'name' => 'Số lệnh',
          'value' => $thongKeLenhTrich[$pg['payment_method_code']]['count'] ?? 0,
          'currency' => $currency,
        ],
      ];
    }

		// Trong truong hop khong co ban ghi, thi se ghi nhan so du vi = 0
		$profileAccount = app(GetTaiKhoanThanhToanViSubAction::class)->getProfileAccount($collectDebtShare->profile_id);
		
		$this->returnData['WALLET'] = [
			[
				'label' => 'payment_method_code',
				'name' => 'Kênh thu',
				'value' => 'WALLET',
				'currency' => $currency,
			],

			[
				'label' => 'so_lenh',
				'name' => 'Số lệnh',
				'value' => $thongKeLenhTrich['WALLET']['count'] ?? 0,
				'currency' => $currency,
			],

			[
				'label' => 'dang_co',
				'name' => 'Đang có',
				'value' => optional($profileAccount)->getSoDuKhaDung() ?? 0, 
				'currency' => $currency,
			],
		];
  }
} // End class