<?php

namespace App\Modules\CollectDebt\Model;

use Illuminate\Database\Eloquent\Model;

class CollectDebtStatisticPartner extends Model
{
	protected $table   = 'debt_recovery_statistic_partner';
	public $timestamps = false;
	protected $guarded = [];
	protected $appends = [];

	public function getTotalAmountSuccess() {
		return $this->mpos_total_repayment + $this->ib_total_repayment + $this->va_total_repayment + $this->wallet_total_repayment;
	}
} // End class
