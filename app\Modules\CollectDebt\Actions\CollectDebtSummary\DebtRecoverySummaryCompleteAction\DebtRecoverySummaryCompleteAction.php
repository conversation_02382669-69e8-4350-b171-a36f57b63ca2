<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCompleteAction;

use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Support\Str;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPlan;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCompleteAction\SubAction\Task\LuuHopDongTatToanDigitalNotiTask;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCompleteAction\SubAction\DanhDauHopDongVer1LaDaTatToanSubAction;

class DebtRecoverySummaryCompleteAction
{
  public function run(CollectDebtSummary $collectDebtSummary)
  {
		$phiQuaHan = $collectDebtSummary->fee_overdue;
		$phiQuaHanDaThanhToan = $collectDebtSummary->fee_overdue_paid;
		$phiQuaHanDuocGiam = $collectDebtSummary->fee_overdue_reduction;


		if ( ($phiQuaHanDaThanhToan + $phiQuaHanDuocGiam) < $phiQuaHan) {
			$msg = "Phi QH ko hop le: " . $collectDebtSummary->contract_code;
			@TelegramAlert::alertTatToan($msg);
			throw new Exception($msg);
		}

		$phiChamKy = $collectDebtSummary->fee_overdue_cycle;
		$phiChamKyDaThanhToan = $collectDebtSummary->fee_overdue_cycle_paid;
		$phiChamKyDuocGiam = $collectDebtSummary->fee_overdue_cycle_reduction;

		if ( ($phiChamKyDaThanhToan + $phiChamKyDuocGiam) < $phiChamKy) {
			$msg = "Phi CK ko hop le: " . $collectDebtSummary->contract_code;
			@TelegramAlert::alertTatToan($msg);
			throw new Exception($msg);
		}

		// Dam bao du tinh thu hoi, cac lich da ve trang thai cuoi
		$countLichChuaHoanThanh = CollectDebtPlan::query()
																						 ->where('contract_code', $collectDebtSummary->contract_code)
																		 				 ->where('status', '!=', CollectDebtEnum::SCHEDULE_STT_DA_HOAN_THANH)
																		 				 ->count();
		if ($countLichChuaHoanThanh > 0) {
			$msg = 'Các lịch của HĐ chưa về trạng thái cuối: ' . $collectDebtSummary->contract_code;
			@TelegramAlert::alertTatToan($msg);
			throw new Exception($msg);
		}
		
		$countLedgerChuaHoanThanh = CollectDebtLedger::query()
																					 ->where('contract_code', $collectDebtSummary->contract_code)
																					 ->whereNotIn('status_summary', [
																							CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT,
																							CollectDebtEnum::LEDGER_STT_ACTION_KHONG_XU_LY
																					 ])
																					 ->count();
																					 
		if ($countLedgerChuaHoanThanh > 0) {
			$msg = 'Các sổ chưa xử lý xong summary: ' . $collectDebtSummary->contract_code;
			@TelegramAlert::alertTatToan($msg);
			throw new Exception($msg);
		}
		

    if (Str::startsWith($collectDebtSummary->contract_code, 'TEST')) {
      $danhDauHopDongLaDaTatToan['contract_id'] = 1; // HĐ fake thì fake luôn
			// HĐ fake thì cũng gọi tất toán luôn
			app(LuuHopDongTatToanDigitalNotiTask::class)->run($collectDebtSummary);
    }else {
      $danhDauHopDongLaDaTatToan = app(DanhDauHopDongVer1LaDaTatToanSubAction::class)->run($collectDebtSummary);
    }

		$otherData = $collectDebtSummary->getSummaryOtherData();
		
    if (isset($danhDauHopDongLaDaTatToan['contract_id'])) {
      $otherData[] = [
        'type' => 'SETTLEMENT',
        'note' => 'Tất toán hợp đồng',
        'time_modified' => time(),
        'data' => [
          'settlement_by' => Helper::getCronJobUser(),
          'contract' => $danhDauHopDongLaDaTatToan
        ]
      ];

      $otherData = collect($otherData)->map(function ($od) {
        if ($od['type'] == 'PAYMENT_METHOD') {
          $od['data'] = collect($od['data'])->map(function ($pm) {
            $pm['closed'] = 'YES';
            return $pm;
          })->all();
        }

        return $od;
      })->all();

      $updateParams = [
        'other_data' => json_encode($otherData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES),
        'status' =>  CollectDebtEnum::SUMMARY_STT_DA_HOAN_THANH,
        'status_contract' => CollectDebtEnum::SUMMARY_STATUS_CONTRACT_DA_TAT_TOAN,
				'is_request_sync' => CollectDebtEnum::SUMMARY_CO_DONG_BO,
				'time_updated' => now()->timestamp,
      ];

      if (empty($collectDebtSummary->time_settlement)) {
        $updateParams['time_settlement'] = time();
      }

      $collectDebtSummary->forceFill($updateParams)->update();

			// Gọi save bản ghi noti tất toán HĐ tại đây
			app(LuuHopDongTatToanDigitalNotiTask::class)->run($collectDebtSummary);
    }

    return $collectDebtSummary;
  }
}
