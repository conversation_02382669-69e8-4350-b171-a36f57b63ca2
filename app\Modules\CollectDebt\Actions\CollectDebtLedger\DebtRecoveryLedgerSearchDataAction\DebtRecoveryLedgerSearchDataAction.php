<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerSearchDataAction;

use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Model\CollectDebtLedger;

class DebtRecoveryLedgerSearchDataAction
{
  public function run(Request $request)
  {
    $collectDebtLedgersPaginate = CollectDebtLedger::query()->with('collectDebtRequestOnly:id,contract_code,partner_request_id,partner_transaction_id,amount_request,payment_method_code');

    $contractCode = trim($request->json('data.filter.contract_code'));
    
    if (!empty($contractCode)) {
      $collectDebtLedgersPaginate->where('contract_code', $contractCode);
    }

    $requestId = trim($request->json('data.filter.request_id'));
    
    if (!empty($requestId)) {
      $collectDebtLedgersPaginate->where('request_id', $requestId);
    }

    $status = trim($request->json('data.filter.status'));
    
    if (!empty($status)) {
      $collectDebtLedgersPaginate->where('status', $status);
    }

		// trang thai hach toan
		$status_summary = trim($request->json('data.filter.status_summary'));
    
    if (!empty($status_summary)) {
      $collectDebtLedgersPaginate->where('status_summary', $status_summary);
    }

		// thoi gian hanh toan tu
		$timeAccoutingFrom = $request->json('data.filter.time_accouting_from', '');
		if (!empty($timeAccoutingFrom)) {
			$from = Carbon::createFromFormat('d-m-Y H:i', $timeAccoutingFrom)->second(0)->timestamp;
			$collectDebtLedgersPaginate->where('time_accounting', '>=', $from);
		}

		// thoi gian hanh toan den
		$timeAccoutingTo = $request->json('data.filter.time_accouting_to', '');
		if (!empty($timeAccoutingTo)) {
			$to = Carbon::createFromFormat('d-m-Y H:i', $timeAccoutingTo)->second(59)->timestamp;
			$collectDebtLedgersPaginate->where('time_accounting', '<=', $to);
		}

    $createFrom = $request->json('data.filter.create_from');
    
    if (!empty($createFrom)) {
      $collectDebtLedgersPaginate->where('time_created', '>=', Carbon::createFromFormat('d-m-Y', $createFrom)->startOfDay()->timestamp);
    }

    $createTo = $request->json('data.filter.create_to');
    if (!empty($createTo)) {
      $collectDebtLedgersPaginate->where('time_created', '<=', Carbon::createFromFormat('d-m-Y', $createTo)->endOfDay()->timestamp);
    }

		$columns = [
			'id',
			'contract_code',
			'plan_ids',
			'request_id',
			'currency',
			'amount',
			'time_record',
			'time_accounting',
			'time_updated_summary',
			'other_data',
			'status',
			'status_summary',
			'status_plan'
		];

    $collectDebtLedgersPaginate = $collectDebtLedgersPaginate->latest('id')->paginate(
      $request->json('data.limit', 10),
      $columns,
      'page',
      $request->json('data.page', 1)
    );

    return $collectDebtLedgersPaginate;
  }
} // End class