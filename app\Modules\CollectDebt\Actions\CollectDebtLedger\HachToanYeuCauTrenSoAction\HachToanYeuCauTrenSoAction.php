<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction;

use DB;
use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask\XuLySoBaoMuonTask;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateAction\SubAction\GetLichThuByIdsSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\DigitalNotiTask\LuuLenhTrichCoTienDigitalNotiTask;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyReportTnexSubAction\SaveGiaoDichTnexSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\HachToanSoThuThieuSubAction\HachToanSoDoiLuongSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\HachToanSoThuThieuSubAction\HachToanSoThuThieuSubAction;
use App\Modules\CollectDebt\Enums\CacheEnum;

class HachToanYeuCauTrenSoAction
{
	private array $__soDaHachToan = [];

	private array $__ledgerIdExcepts = [];

	public function initHachToan()
	{
		mylog(['enter thuc hien hach toan' => 'ok']);

		for ($i = 1; $i <= 30; $i++) {
			try {
				$collectDebtLedger = $this->run();

				if ($collectDebtLedger == 'EMPTY') {
					$this->__soDaHachToan[] = 'Khong co so nao can thuc hien hach toan';
					break;
				}

				if ($collectDebtLedger && optional($collectDebtLedger)->id) {
					$this->__soDaHachToan[] = $collectDebtLedger->id;
				}
			} catch (\Throwable $th) {
				mylog(['Loi hach toan' => Helper::traceError($th)]);
				@TelegramAlert::sendAccouting(Helper::traceError($th));
				continue;
				// throw $th; // sau phai bo dong nay
				continue;
			} finally {
				usleep(300000);
			}
		}

		return $this->__soDaHachToan;
	}

  public function run()
  {
    $collectDebtLedgerCurrent = CollectDebtLedger::query()
																								 ->where('status', CollectDebtEnum::LEDGER_STT_CHUA_XU_LY);

		if (!empty($this->__ledgerIdExcepts)) {
			$collectDebtLedgerCurrent = $collectDebtLedgerCurrent->whereNotIn('id', $this->__ledgerIdExcepts);
		}
		
		$collectDebtLedgerCurrent = $collectDebtLedgerCurrent->first();

    if (!$collectDebtLedgerCurrent) {
			mylog(['[EMPTY]' => 'khong co thong tin so can hach toan']);
			return 'EMPTY';
		}
    
		$updatedLenDangXuLy = CollectDebtLedger::query()
																					 ->where('id', $collectDebtLedgerCurrent->id)
																					 ->where('status', CollectDebtEnum::LEDGER_STT_CHUA_XU_LY)
																					 ->update([
																						'status' => CollectDebtEnum::LEDGER_STT_DANG_HACH_TOAN
																					 ]);
		if (!$updatedLenDangXuLy) {
			$this->__ledgerIdExcepts[] = $collectDebtLedgerCurrent->id;
			mylog(['[LOI]' => 'khong the cap nhat so thang DANG HACH TOAN']);
			throw new Exception('khong the cap nhat so thang DANG HACH TOAN');
		}

		$collectDebtLedger = CollectDebtLedger::find($collectDebtLedgerCurrent->id); // khong dung refresh cho nay

		if ($collectDebtLedger->status != CollectDebtEnum::LEDGER_STT_DANG_HACH_TOAN) {
			$this->__ledgerIdExcepts[] = $collectDebtLedgerCurrent->id;

			mylog(['[LOI]' => 'du lieu chua o trang thai dang hach toan, tu choi xu ly']);
			throw new Exception('du lieu chua o trang thai dang hach toan, tu choi xu ly');
		}

		if ($collectDebtLedgerCurrent->amount != $collectDebtLedger->amount) {
			$this->__ledgerIdExcepts[] = $collectDebtLedgerCurrent->id;

			$updateVeChuaXuLy = CollectDebtLedger::query()
																					 ->where(['id' => $collectDebtLedger->id, 'status' => CollectDebtEnum::LEDGER_STT_DANG_HACH_TOAN])
																					 ->update(['status' => CollectDebtEnum::LEDGER_STT_CHUA_XU_LY]);

			mylog(['[LOI NGHIEM TRONG]' => 'Amount ghi so dang bi sai thong tin']);

			throw new Exception('Amount ghi so dang bi sai thong tin');
		}

		mylog(['[BAN GHI SO]' => $collectDebtLedger]);
		$this->__ledgerIdExcepts[] = $collectDebtLedgerCurrent->id;

		DB::beginTransaction();
		try {
			// Xu ly neu la so bao muon
			if ($collectDebtLedger->isSoDoiTacBaoMuon() || $collectDebtLedger->isSoThuKhongCoLich()) {
				mylog(['[SO BAO MUON hoac SO THU KHONG CO LICH]' => 'YES']);
				$updatedSoBaoMuon = app(XuLySoBaoMuonTask::class)->run($collectDebtLedger);

				if (!$updatedSoBaoMuon) {
					mylog(['[LOI UPDATE SO BAO MUON]' => $updatedSoBaoMuon]);
					throw new Exception('[LOI UPDATE SO BAO MUON]');
				}

				app(SaveGiaoDichTnexSubAction::class)->run($collectDebtLedger);
				DB::commit();
				return $collectDebtLedger;
			}
			
			$collectDebtLedger = $collectDebtLedger->load('collectDebtRequest');

			$collectDebtLedger->schedules = $this->getLichThuCuaSo($collectDebtLedger);
			
			// Xu ly hach toan
			mylog(['
				[SO CAN HACH TOAN]' => 'YES',
				'BAN GHI SO' => $collectDebtLedger
			]);

			//app(LuuLenhTrichCoTienDigitalNotiTask::class)->run($collectDebtLedger);
			
			$ledgerDaHachToan =  app(HachToanSoDoiLuongSubAction::class)->run($collectDebtLedger);
			app(SaveGiaoDichTnexSubAction::class)->run($collectDebtLedger);

			DB::commit();
			return $ledgerDaHachToan;
		}catch(\Throwable $th) {
			mylog(['[LOI TRANSACTION]' => Helper::traceError($th)]);

			DB::rollBack();
			$updatedVeChuaXuLy = CollectDebtLedger::query()
																						->where(['id' => $collectDebtLedger->id, 'status' => CollectDebtEnum::LEDGER_STT_DANG_HACH_TOAN])
																						->update(['status' => CollectDebtEnum::LEDGER_STT_CHUA_XU_LY]);
			if (!$updatedVeChuaXuLy) {
				mylog(['[LOI ROLLBACK]' => Helper::traceError($th)]);
				throw new Exception('LOI ROLLBACK, khong the cap nhat ban ghi so ve CHUA XU LY');
			}

			throw $th;
		}
  }

	public function getLichThuCuaSo(CollectDebtLedger $collectDebtLedger): Collection
	{
		if (empty($collectDebtLedger->plan_ids)) {
			return Collection::make();
		}

		$plans = app(GetLichThuByIdsSubAction::class)->run($collectDebtLedger->plan_ids);

		mylog(['[DANH SACH SO CUA LICH]' => $plans]);
		
		return $plans;
	}
} // End class