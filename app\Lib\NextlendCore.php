<?php

namespace App\Lib;

use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class NextlendCore
{
	protected $_API_URL            = '';
	protected $_API_APP_ID         = '';
	protected $_API_APP_SECRET_KEY = '';
	protected $_USERNAME_REQUEST   = 'guest';
	protected $_USERID_REQUEST     = '1';
	protected $_API_KET_ENCRYPTION_DATA = '';

	private $__coreResponseResult;

	public function __construct()
	{
		$this->_API_URL = config('nextlend.NEXTLEND_CORE_API_URL');
		$this->_API_APP_SECRET_KEY = config('nextlend.NEXTLEND_CORE_API_APP_SECRET_KEY');
		$this->_API_APP_ID = config('nextlend.NEXTLEND_CORE_API_APP_ID');
		$this->_API_KET_ENCRYPTION_DATA = config('nextlend.NEXTLEND_CORE_API_KET_ENCRYPTION_DATA');
	}

	/**
	 * Method call
	 *
	 * @param array $inputs Là các tham số trong request muốn truyền để truy vấn
	 *
	 * @return void
	 */
	public function callRequest(array $inputs = [], string $functionName = '', string $method = 'post')
	{
		$logContext = [
			'func' => $functionName,
			'inputRaw' => $inputs,
		];

		$requestInput = $this->_makeRequestInput($inputs, $functionName, ['id' => $inputs['admin_user_id'] ?? $this->_USERID_REQUEST]);

		$logContext['inputHash'] = $requestInput;

		$headerParam = [
			'app_id' => $this->_API_APP_ID,
			'app_secret_key' => $this->_API_APP_SECRET_KEY,
			'cache-control' => 'no-cache'
		];

		$response = Http::timeout(config('nextlend.NL_PARTNER_TIMEOUT', 5))
		->withHeaders($headerParam)
		->withOptions([
			'debug' => false,
			'verify' => false,
			'connect_timeout' => config('nextlend.NL_PARTNER_CONNECTION_TIMEOUT', 3)
		])->retry(1, 100);

		$result = "START";

		try {
			$method = trim(strtolower($method));

			switch ($method) {
				case 'post':
					$result = $response->asForm()->post($this->_API_URL, $requestInput);
					break;

				case 'get':
					$result = $response->get($this->_API_URL, $requestInput);
					break;

				case 'put':
					$result = $response->asForm()->put($this->_API_URL, $requestInput);
					break;

				default:
					$result = $response->asForm()->post($this->_API_URL, $requestInput);
					break;
			}
		} catch (\Throwable $th) {
			$logContext['error'] = $th->getMessage();
			throw $th;
		}

		$json = $result->json();
		$logContext['result'] = $json;

		if (!empty($logContext['result']['errorCode']) && $logContext['result']['errorCode'] == strtotime('success')) {
			unset($logContext['inputHash']);
		}

		$this->__coreResponseResult = $json;

		Log::info("[NextlendCore][$functionName] ---->", $logContext);
		return $this;
	}

	public function _makeRequestInput(array $params = [], string $func = '', array $userHandler = []): array
	{
		$userRequestId = !empty($userHandler['id']) ? $userHandler['id'] : $this->_USERID_REQUEST;
		$hash          = $this->_buildHash($params, $userRequestId);

		$chuoiTruocMd5 = $func . $hash . $this->_API_APP_SECRET_KEY;

		$checkSum      = md5($chuoiTruocMd5);


		return [
			'func'             => $func,
			'checksum'         => $checkSum,
			'params'           => $hash,
			'username_request' => !empty($userHandler['name']) ? $userHandler['name'] : $this->_USERNAME_REQUEST,
			'users_request_id' => $userRequestId,
			'language'         => 'vi',
			'client_ip'        => request()->ip()
		];
	}

	protected function _buildHash(array $params = [], $userRequestId = ''): string
	{
		$params['site'] = 1;
		$params['admin_user_id'] = $userRequestId;

		$paramsJsonEncode = json_encode($params, true);

		$paramsEncrypt = Encryption::Encrypt($paramsJsonEncode, $this->_API_KET_ENCRYPTION_DATA);
		return $paramsEncrypt;
	}

	public function decryptData($needAnalysis = false)
	{

		if (!$this->__coreResponseResult || !$this->__coreResponseResult['errorCode'] || $this->__coreResponseResult['errorCode'] != strtolower('success')) {
			// Dùng khi xử lý verify OTP
			if ($needAnalysis) {
				return $this->__coreResponseResult;
			}

			throw new \Exception('Lỗi gọi hệ thống Core đối tác: ' . $this->__coreResponseResult['errorDescription'], 500);
		}

		$value = [];

		try {
			$result = Encryption::Decrypt($this->__coreResponseResult['data'], $this->_API_KET_ENCRYPTION_DATA);
			$value = json_decode($result, true);
		} catch (\Throwable $e) {

			throw new Exception("Can not decrypt value: " . $e->getMessage());
		} finally {
			return $value;
		}
	}
} // End class