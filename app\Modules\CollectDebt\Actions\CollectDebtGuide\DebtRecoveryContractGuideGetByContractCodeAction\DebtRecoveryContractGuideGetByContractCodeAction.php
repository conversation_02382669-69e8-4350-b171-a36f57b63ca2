<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideGetByContractCodeAction;

use Exception;
use Illuminate\Support\Arr;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Actions\CollectDebtConfig\ConfigButtonShowOffAction;

class DebtRecoveryContractGuideGetByContractCodeAction
{
	public array $summaryFields = [
		'id',
    'contract_code',
    'contract_type',
    'contract_cycle',
    'contract_intervals',
    'contract_amount',
    'fee_overdue',
    'fee_overdue_cycle',
    'total_amount_receiver',
    'total_amount_excess_revenue',
    'total_amount_paid',
    'total_fee_paid',
    'fee_overdue_paid',
    'fee_overdue_cycle_paid',
    'fee_overdue_reduction',
    'fee_overdue_cycle_reduction',
    'fee_overdue_refund',
    'fee_overdue_cycle_refund',
    'total_amount_repayment_debt',
    'total_amount_excess_refund',
    'total_amount_refund',
    'amount_refunding',
    'is_overdue',
    'is_over_cycle',
    'is_send_mail_slow_cycle',
    'is_send_mail_overdue',
    'is_send_noti_overdue',
    'is_request_sync',
    'number_over_cycle',
    'number_day_overdue',
    'description',
    'status',
    'status_contract',
    'time_overdued',
    'time_created',
    'time_updated',
    'time_canceled',
    'time_accounting',
    'time_complated',
    'time_settlement',
    'partner_code',
    'profile_id',
    'is_fully_paid',
		'other_data'
	];
	/**
	 * API lấy chi tiết HĐ (và không dùng trường status do collectDebtShare appends ra)
	 *
	 * @param string $contract_code	[Mã hợp đồng]
	 * @return CollectDebtShare
	 */
  public function run(string $contract_code = '')
  {
		$columns = [
			'id',
			'contract_code',
			'contract_data',
			'profile_data',
			'payment_guide',
		];

		if (!empty(request()->json('data.fields'))) {
			$fields = request()->json('data.fields', []);
			$columns = array_merge($columns, $fields);
		}

    $collectDebtShare = CollectDebtShare::query()
                                        ->with(['configPauseJob' => function ($query) {
                                          return $query->wherePaused();
                                        }])
                                        ->where('contract_code', $contract_code)
                                        ->first($columns);
    throw_if(!$collectDebtShare, new Exception('Không tìm thấy chỉ dẫn'));

    $collectDebtSummary = CollectDebtSummary::where('contract_code', $contract_code)->first($this->summaryFields);

    $paymentMethods = $collectDebtSummary->getSummaryOtherDataItem('PAYMENT_METHOD');

    $paymentMethodStatus = collect($paymentMethods['data'])->map(function ($pm) {
      return [
        'payment_method_code' => $pm['payment_method_code'],
        'closed' => $pm['closed']
      ];
    });
  
    $collectDebtShare->payment_methods = $paymentMethodStatus;
    $collectDebtShare->time_settlement = $collectDebtSummary->time_settlement;
    $collectDebtShare->currency = 'VND';
		$collectDebtShare->contract_status_before = $collectDebtSummary->isHopDongDaTatToan() ? 'DA_TAT_TOAN' : 'CHUA_TAT_TOAN';
		
		$collectDebtShare->time_complated = $collectDebtSummary->time_complated;
		$shareContractData = $collectDebtShare->getShareContractData();
		$collectDebtShare->partner_disbursement = $shareContractData['contract_partner']['partner_code'] ?? 'NEXTLEND';

		$collectDebtShareArray = $collectDebtShare->toArray();
		$collectDebtShareArray['contract_data'] = $this->__getContractData($collectDebtShare);
		$collectDebtShareArray['profile_data'] = $this->__getProfileData($collectDebtShare);
		
		$collectDebtShareArray['buttonSate'] = app(ConfigButtonShowOffAction::class)->run($collectDebtSummary);
    return $collectDebtShareArray;
  }

	public function __getContractData(CollectDebtShare $collectDebtShare) {
		$contractData = json_decode($collectDebtShare->contract_data, true);
		
		$fields = Arr::only($contractData, [
			'contract_fee',
			'contract_receiver',
			'time_completed',
			'time_end_as_vn_date',
			'time_settlement',
			'status',
		]);

		return json_encode($fields);
	}

	public function __getProfileData(CollectDebtShare $collectDebtShare) {
		$profileData = json_decode($collectDebtShare->profile_data, true);
		
		$fields['merchant'] = Arr::only($profileData['merchant'], [
			'fullname',
			'email',
			'partner_merchant_code',
			'mobile',
			'partner_merchant_code_old',
			'email_old',
		]);

		return json_encode($fields);
	}
} // Emd class
