<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\ThucHienCaiTienGhiSoAction;

use DB;
use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\ThucHienCaiTienGhiSoAction\SubAction\TaoBanGhiLedgerSubAction;

class ThucHienCaiTienGhiSoAction
{
	private array $__yeuCauDaGhiSo = [];

	public function initGhiSo(Request $request)
	{
		mylog(['enter thuc hien ghi so' => 'ok']);

		for ($i = 1; $i <= 30; $i++) {
			try {
				$collectDebtRequest = $this->run($request);

				if ($collectDebtRequest == 'EMPTY') {
					$this->__yeuCauDaGhiSo[] = 'khong co yeu cau nao can ghi so';
					break;
				}

				if ($collectDebtRequest && $collectDebtRequest->id) {
					$this->__yeuCauDaGhiSo[] = $collectDebtRequest->id;
				}
			} catch (\Throwable $th) {
				mylog(['loi ghi so' => Helper::traceError($th)]);
				@TelegramAlert::sendGhiSo(Helper::traceError($th));
				continue;
			} finally {
				usleep(300000);
			}
		}

		return $this->__yeuCauDaGhiSo;
	}


	public function run()
	{
		$collectDebtRequest = CollectDebtRequest::query()
			->where('status_recored', CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO)
			->where(function ($q) {
				$q->orWhere(function ($query) {
					// Trích MPOS cho cae 2 luồng
					$query->where('payment_method_code', 'MPOS')
						->whereIn('status', [CollectDebtEnum::REQUEST_STT_DA_DUYET, CollectDebtEnum::REQUEST_STT_CAN_HOAN_THANH_VA_KIEM_TRA])
						->where('status_payment', CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA);
				})
				->orWhere(function ($query) {
					// Tự động
					$query->where('create_from', CollectDebtEnum::REQUEST_LOAI_TRICH_TU_DONG)
						->where('status', CollectDebtEnum::REQUEST_STT_DA_DUYET)
						->where('status_payment', CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA);
				})
				->orWhere(function ($query) {
					// Tất toán giảm phí
					$query->where('create_from', CollectDebtEnum::REQUEST_LOAI_TRICH_TAY)
						->whereHas('collectDebtRequestActions', function ($q) {
							$q->where('action_code', 'APPROVE2')
								->where('type', CollectDebtEnum::REQUEST_ACTION_TYPE_GIAM_PHI);
						});
				});
			})->first();

		if (!$collectDebtRequest) {
			mylog(['khong co yc nao de thuc hien ghi so' => 'Ok']);
			return 'EMPTY';
		}

		mylog([
			'---------------------------------------' => '-------------------------------',
			'yeu cau dang xu ly ghi so' => $collectDebtRequest
		]);


		$updateLenDangGhiSo = CollectDebtRequest::query()
			->where('id', $collectDebtRequest->id)
			->where('status_recored', CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO)
			->update([
				'status_recored' => CollectDebtEnum::REQUEST_STT_RC_DANG_GHI_SO
			]);

		if (!$updateLenDangGhiSo) {
			mylog(['loi khong the update len trang thai DANG GHI SO' => $updateLenDangGhiSo]);
			throw new Exception('loi khong the update len trang thai DANG GHI SO');
		}

		$collectDebtRequest = CollectDebtRequest::find($collectDebtRequest->id); // khong dung refresh

		@mylog(['du lieu YEU CAU sau khi REFRESH' => $collectDebtRequest]);

		if ($collectDebtRequest->status_recored != CollectDebtEnum::REQUEST_STT_RC_DANG_GHI_SO) {
			mylog(['Ban ghi yeu cau dang khong o trang thai DANG GHI SO. Trang thai hien tai la: ' => $collectDebtRequest->status_recored]);
			throw new Exception('ban ghi yeu cau dang khong o trang thai DANG GHI SO');
		}

		DB::beginTransaction();
		try {
			// cho ghi so vi khong co partner
			if ($collectDebtRequest->isYeuCauTrichLichThuQuaKhu()) {
				mylog(['la lenh trich qua khu, kong co partner' => 'ok']);
				$yeuCauGhiSoThanhCong = $this->__thucHienGhiSo($collectDebtRequest);
				DB::commit();
				return $yeuCauGhiSoThanhCong;
			}

			// cho ghi so vi khong co partner
			if ($collectDebtRequest->isTrichTayGiamPhi()) {
				mylog(['la yeu cau tat toan giam phi, khong co partner' => 'ok']);
				$yeuCauGhiSoThanhCong = $this->__thucHienGhiSo($collectDebtRequest);
				DB::commit();
				return $yeuCauGhiSoThanhCong;
			}

			mylog(['la yeu cau trich no thong thuong' => 'ok']);

			$collectDebtPartner = CollectDebtPartner::query()
				->where('payment_method_code', $collectDebtRequest->payment_method_code)
				->where('partner_request_id', $collectDebtRequest->partner_request_id)
				->first();

			if (!$collectDebtPartner) {
				mylog(['loi khong co partner cua yc' => 'ok']);
				throw new Exception('khong co thong tin partner cua yc');
			}

			mylog(['partner cua yc la' => $collectDebtPartner]);

			@mylog([
				'partner_request_id' => $collectDebtPartner->partner_request_id,
				'so_tien_thanh_cong_cua_yc' => $collectDebtRequest->amount_receiver,
				'so_tien_thanh_cong_cua_partner' => $collectDebtPartner->amount_receiver,
				'ban ghi yc luc nay la' => $collectDebtRequest
			]);

			// Kiem tra so tien tren partner va so tien thanh cong tren yeu cau
			if ($collectDebtPartner->amount_receiver != $collectDebtRequest->amount_receiver) {
				mylog([
					'phat hien so tien cua partner khac voi so tien cua yeu cau' => 'ok',
					'so tien tren partner' => $collectDebtPartner->amount_receiver,
					'so tien tren yeu cau' => $collectDebtRequest->amount_receiver
				]);


				if ($collectDebtPartner->payment_method_code == 'MPOS') {
					mylog(['[LOI MPOS]' => 'So tien thanh cong tren yc bi khac voi partner']);

					// so tien trich thuc te
					$soTienTrichTc = $collectDebtPartner->amount_receiver;
					
					$collectDebtRequest->amount_receiver = $soTienTrichTc;
					$collectDebtRequest->status_recored = CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO;
					$result = $collectDebtRequest->save();

					if (!$result) {
						mylog(['[LOI]' => 'So the cap nhat so tien trich thuc te ve cho yc']);
						throw new Exception('So tien thanh cong tren yc bi khac voi partner');
					}

					DB::commit();
					return $collectDebtRequest;
				}
				
				throw new Exception('phat hien so tien cua partner khac voi so tien cua yeu cau');
			}

			$yeuCauGhiSoThanhCong = $this->__thucHienGhiSo($collectDebtRequest);
			DB::commit();

			return $yeuCauGhiSoThanhCong;
		} catch (\Throwable $th) {
			mylog(['loi ghi so' => Helper::traceError($th)]);

			DB::rollBack();

			$updateVeTrangThaiChuaGhiSo = CollectDebtRequest::query()
				->where('id', $collectDebtRequest->id)
				->where('status_recored', CollectDebtEnum::REQUEST_STT_RC_DANG_GHI_SO)
				->update([
					'status_recored' => CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO
				]);
			if (!$updateVeTrangThaiChuaGhiSo) {
				mylog(['[LOI] - RollBack ve trang thai chua ghi so: ' => $updateVeTrangThaiChuaGhiSo]);
			}

			throw $th;
		}
	}

	private function __thucHienGhiSo(CollectDebtRequest $collectDebtRequest)
	{
		$collectDebtLedger = app(TaoBanGhiLedgerSubAction::class)->run($collectDebtRequest);

		if (!$collectDebtLedger) {
			mylog(['khong the ghi so. Ket qua ghi so la:' => $collectDebtLedger]);
			throw new Exception('khong the tao ban ghi ghi so.');
		}

		$requestOtherData = $collectDebtRequest->getRequestOtherData();

		$updateSoVeTrangThaiDaGhiSo = CollectDebtRequest::query()
			->where('id', $collectDebtRequest->id)
			->update([
				'status_recored' => CollectDebtEnum::REQUEST_STT_RC_DA_GHI_SO,
				'time_recored' => now()->timestamp,
				'recored_by' => Helper::getCronJobUser(),

				'time_completed' => time(),
				'completed_by'  => Helper::getCronJobUser(),

				'other_data' => json_encode($requestOtherData),
			]);

		if (!$updateSoVeTrangThaiDaGhiSo) {
			mylog(['khong the cap nhat trang thai ghi so thanh cong. Loi' => $updateSoVeTrangThaiDaGhiSo]);
			throw new Exception('[THAT BAI] - khong the cap nhat trang thai la: DA GHI SO');
		}

		return $collectDebtRequest;
	}
}  // End class