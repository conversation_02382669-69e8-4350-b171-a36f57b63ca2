<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateAction;

use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequest;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateAction\SubAction\CapNhatCycleNumberSubAction;

class DebtRecoveryContractPlanCreateAction
{  
  /**
   * Method run
   *
   * @param DebtRecoveryContractPlanCreateRequest $request [explicite description]
   *
   * @return array [\
   *  'create_schedule_result_success' => 0: Fail | 1: Success
   *  'collect_debt_schedule' => [Danh sách bản ghi]
   * ]
   */
  public function run(array $params=[])
  {
    $returnData = [
      'create_schedule_result_success' => false,
      'collect_debt_schedule' =>  collect([])
    ];

    // CollectDebtSchedule::where('contract_code', $params[0]['contract_code'])->delete();

    $insertResult = CollectDebtSchedule::insert($params);
    
    $plans = CollectDebtSchedule::where('contract_code', $params[0]['contract_code'])->orderBy('id', 'ASC')->get();
    
    $returnData = [
      'create_schedule_result_success' => $insertResult,
      'collect_debt_schedule' => $plans 
    ];     

    return $returnData;
  }
} // End class