<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFinishCutOffTimeAction;

use Exception;
use GuzzleHttp\Pool;
use App\Lib\TelegramAlert;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Modules\CollectDebt\Ultilities\BatchUtil;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtProcessing;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFinishCutOffTimeAction\SubAction\XuLyYeuCauTrichQuaDoiTacSubAction;

class CutOffRequestBatchAction extends BatchProcessingAction
{
	public $timeout = 90;

	public function __construct()
	{
		ini_set('max_execution_time', $this->timeout);
	}

	public function run()
	{
		$r = CollectDebtProcessing::query()
			->where('expired_at', '<', now())
			->where('is_cutoff', 0)
			->selectRaw("MIN(id) as min_id, MAX(id) as max_id")
			->first();

		if (empty($r->min_id) && empty($r->max_id)) {
			return ['msg' => 'No data'];
		}

		$ranges = BatchUtil::getRanges($r->min_id, $r->max_id, $this->batchCount, $this->batchSize);

		$processResult = $this->processBatch($ranges);
		return $processResult;
	}

	private function processBatch($ranges)
	{
		$client = parent::createHttpClient($this->timeout - 10);

		$baseUrl = config('app.url');

		$requests = function () use ($ranges, $baseUrl) {
			foreach ($ranges as $r) {
				$url = sprintf('%s/HandleCutOffRequest?min=%s&max=%s', $baseUrl, $r[0], $r[1]);
				yield $r => new Request('GET', $url);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => $this->batchCount,
			'fulfilled' => function ($response, $r) {
				// $body = (string)$response->getBody();
			},
			'rejected' => function (\Throwable $reason, $r) {
				$msg = "[CutOffRequest --->range error: " . json_encode($r) . '] failed: ' . $reason->getMessage();
				Log::info($msg);
			},
		]);

		$promise = $pool->promise();
		$promise->wait();

		unset($pool, $promise);

		return ['msg' => 'all done'];
	}

	public function HandleCutOffRequest()
	{
		$min = request()->get('min');
		$max = request()->get('max');

		$listProcessing = CollectDebtProcessing::query()->with([
			'collectDebtRequest.collectDebtPartnerRequestId',
			'collectDebtRequest.collectDebtShare'
		])
			->whereBetween('id', [$min, $max])
			->get();

		$listProcessing->each(function (CollectDebtProcessing $p) {
			$collectDebtRequest = $p->collectDebtRequest;

			if ($collectDebtRequest->time_expired > now()->timestamp) {
				return $p;
			}


			if ($collectDebtRequest->collectDebtPartnerRequestId || $collectDebtRequest->status_cutoff == CollectDebtEnum::REQUEST_DA_XL_CUT_OFF) {
				$this->__markAsComplete($collectDebtRequest, $p);
				return $p;
			}

		
			try {
				// Chưa có partner || không có mã chứng từ
				$coreContract = $collectDebtRequest->collectDebtShare->getCoreContractByGuide();
				$rq = app(XuLyYeuCauTrichQuaDoiTacSubAction::class)->run($collectDebtRequest, $coreContract);

				$this->__markAsComplete($collectDebtRequest, $p);


				return $p;
			} catch (\Throwable $th) {
				@TelegramAlert::alertCutOff($th->getMessage());

				throw new Exception($th->getMessage());
			}

			return $partnerRequestId;
		});
	}

	private function __markAsComplete(CollectDebtRequest $collectDebtRequest, CollectDebtProcessing $p)
	{
		return DB::transaction(function () use ($collectDebtRequest, $p) {
			$this->__updateVeDaXuLyCutOff($collectDebtRequest);
			$this->__updatedProcessing($p);
		});
	}

	private function __updateVeDaXuLyCutOff(CollectDebtRequest $collectDebtRequest)
	{
		$r = CollectDebtRequest::query()
			->where('id', $collectDebtRequest->id)
			->update([
				'status_cutoff' => CollectDebtEnum::REQUEST_DA_XL_CUT_OFF,
				'time_updated' => now()->timestamp
			]);

		if (!$r) {
			throw new Exception('Loi cap nhat request');
		}

		return true;
	}

	private function __updatedProcessing(CollectDebtProcessing $p)
	{
		$p->is_cutoff = 1;
		$p->updated_at = now();
		$r = $p->save();

		if (!$r) {
			throw new Exception('Loi cap nhat processing');
		}

		return true;
	}
}  // End class