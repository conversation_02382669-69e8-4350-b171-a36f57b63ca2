<?php

namespace App\Modules\CollectDebt\Model\Traits\CollectDebtRequest;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Str;

trait RequestStatusable
{
  public function isYeuCauTrichTuDong(): bool
  {
    return $this->create_from == 1 && $this->payment_channel_code == 'MPOS';
  }

  public function isYeuCauTrichTay(): bool
  {
      return $this->create_from == 2;
  }

  public function isStatusDaHoanThanh(): bool
  {
    return $this->status == CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH;
  }

  public function isStatusDaTuChoi(): bool
  {
    return $this->status == CollectDebtEnum::REQUEST_STT_TU_CHOI;
  }

  public function isCoTheTuChoi(): bool {
    return !$this->isRecorded() && !in_array($this->status, [
      CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH,
      CollectDebtEnum::REQUEST_STT_TU_CHOI
    ]);
  }

  public function isYeuCauChuaHetHan(): bool {
    return $this->time_expired > time();
  }

  public function isCoTheCheckPartner(): bool {
    return $this->payment_method_code == 'MPOS' && !empty($this->partner_transaction_id)
                                                && !Str::contains($this->partner_transaction_id, 'MANUAL');
  }

  public function isTrichTayThuCong(): bool {
		return $this->isManualDebt() && $this->isHinhThucTrich() != 'GIAM_PHI';
	}

	public function isTrichTayGiamPhi(): bool {
		return $this->isManualDebt() && $this->isHinhThucTrich() == 'GIAM_PHI';
	}

	public function isTrichTayThuCongThongThuong(): bool {
		return $this->isManualDebt() && $this->isHinhThucTrich() != 'GIAM_PHI' && $this->isTrichNgay() != 'YES';
	}

	public function isRutTienNhanh(): bool {
		return $this->isManualDebt() && $this->isHinhThucTrich() != 'GIAM_PHI' && $this->isTrichNgay() == 'YES';
	}

	public function isChuaDuyetTrichTayThuCong(): bool {
		return $this->isTrichTayThuCong() &&  empty($this->time_approved) && $this->isChuaHuyTrichTay();
	}

	public function isDaDuyetTrichTayThuCong(): bool {
		return $this->isTrichTayThuCong() &&  !empty($this->time_approved) && $this->isChuaHuyTrichTay();
	}

	public function isLenhTrichNgayDaHachToanNhungChuaBiHuyVaChuaCoKetQuaTrich(): bool {
		$isDaGhiSo = !empty($this->time_recored);
		$isDaHoanThanh = !empty($this->getRawOriginal('time_completed'));
		
		if (!$isDaGhiSo || !$isDaHoanThanh) {
			return false;
		}

		if ($this->isRutTienNhanh()) {
			// Đã hạch toán lệnh rút tiền nhanh, kiểm tra tiếp bị hủy chưa, nếu hủy rồi thì là false
			if (!empty($this->time_canceled)) {
				return false;
			}

			// Đã nhận kết quả thì là false
			if (!empty($this->time_receivered)) {
				return false;
			}

			return true;
		}

		return false;
	}

	public function isYeuCauCoYeuToHuy(): bool {
		$requestOtherData = $this->getRequestOtherData();
		return collect($requestOtherData)->contains('type', 'CANCEL') || !empty($this->time_canceled);
	}

	public function isCoTheTaoDieuChinh(): bool {
		return $this->payment_method_code == 'MPOS' && $this->status == CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH
																								&& $this->create_from == CollectDebtEnum::REQUEST_LOAI_TRICH_TU_DONG
																								&& !empty($this->partner_transaction_id)
																								&& $this->amount_receiver > 0;
	}
} // End class
