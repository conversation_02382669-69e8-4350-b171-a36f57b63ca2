<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyMoLaiLichSA;

use Exception;
use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtRequestAdjustment;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask\CongTienQuaHanTask;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateAction\SubAction\GetLichThuByIdsSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask\IsCoLichThuPhiQuaHanTask;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\FeeHandler\XuLyThanhToanPhiFH;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyMoLaiLichSA\Task\TaoLichThuVetNoGocMLLTask;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyMoLaiLichSA\Task\TaoLichThuPhiChamKyMLLTask;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyMoLaiLichSA\Task\TaoLichThuPhiQuaHanMLLTask;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyMoLaiLichSA\Task\TaoLichThuVetPhiConLaiMLLTask;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyMoLaiLichSA\Task\UpdateDieuChinhOtherDataPlanMLLTask;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyMoLaiLichSA\Task\CheckLichMoLaiDaSinhPhiQuaHanMLLTask;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule\PlanSortableCollectionByRule;
use Illuminate\Database\Eloquent\Collection;

class XuLyMoLaiLichSA
{
  public $metaData = [];

	public $listLichThuDuocSinhRa;

  private float $__soTienTrichThanhCong = 0;

	public function __construct()
	{
		$this->listLichThuDuocSinhRa = Collection::make([]);
	}

  public function run(CollectDebtRequestAdjustment $ra): array
  {
    $collectDebtShare = CollectDebtShare::query()->where('contract_code', $ra->contract_code)->first();

    $this->__soTienTrichThanhCong = $ra->getSoTienTrichThanhCongThucTe();

		$collectDebtPartner = CollectDebtPartner::query()->with('collectDebtRequest:partner_request_id,plan_ids')
																										 ->where([
																											'id' => $ra->reference_id,
																											'contract_code' => $ra->contract_code
																										 ])
																										 ->select(['id', 'contract_code', 'partner_request_id'])
																										 ->first();
		
		if (!$collectDebtPartner) {
			throw new Exception('Khong tim thay thong tin ban ghi partner');
		}	

		$planIds = $collectDebtPartner->collectDebtRequest->plan_ids;
		
    $plans = app(GetLichThuByIdsSubAction::class)->run($planIds);
    $plans = app(PlanSortableCollectionByRule::class)->sortCollection($plans);

		/**
		 * Tu tinh toan ra so tien con phai thu tiep tren tung lich:
		 * 		+ Tu do tao ra 1 lich tuong duong de thu tiep so tien con thieu
		 * 		+ Thuc hien sinh phi neu lich dang xu ly la lich (C)
		 */
    $plans->map(function (CollectDebtSchedule $plan) use ($ra, $collectDebtShare) {
			mylog(['--------------------------------------' => '--------------------------------------']);
			mylog(['PlanID' => $plan->id, 'ContractCode' => $plan->contract_code]);

      $soTienThuThanhCongTruocDo = $plan->success_amount_debit;
			mylog(['So Tien Trich Ngay Da Gach Lich Truoc Do' => $soTienThuThanhCongTruocDo]);

      $soTienGhiNhanThanhCongChoLich = min($soTienThuThanhCongTruocDo, $this->__soTienTrichThanhCong);
			mylog(['So tien ghi nhan trich thanh cong thuc te' => $soTienGhiNhanThanhCongChoLich]);

			// Giam tru so tien trich thanh cong xuong
      $this->__soTienTrichThanhCong -= $soTienGhiNhanThanhCongChoLich;

      $soTienConPhaiThuTiep = $plan->getSoTienMoLaiLichPhaiThu($soTienGhiNhanThanhCongChoLich);
			mylog(['So tien con phai thu tiep cua lich la:' => $soTienConPhaiThuTiep]);

			// Set so tien thu hoi thuc te vao lich
      $plan->success_amount_debit = $soTienGhiNhanThanhCongChoLich;

      $this->metaData[] = ['label' => CollectDebtEnum::METADATA_CAN_TRU_TIEN_VAO_LICH, 'value' => $soTienGhiNhanThanhCongChoLich];

      if ($soTienConPhaiThuTiep > 0) {
       
        if ($plan->isLichThuGoc()) {
					mylog(['La lich thu goc' => 'yes']);

          if ($plan->isKhongPhaiLichTatToan()) {
						mylog(['Khong phai lich tat toan' => 'yes']);

						// Tao lich thu not so tien con phai thu tiep
						$lichThuVetNoGoc = app(TaoLichThuVetNoGocMLLTask::class)->run($plan, $soTienConPhaiThuTiep, $soTienGhiNhanThanhCongChoLich);
						$this->listLichThuDuocSinhRa->push($lichThuVetNoGoc);

						mylog(['ID lich thu vet no goc cho so tien con lai' => $lichThuVetNoGoc->id]);

            if ($plan->isLichThuChinh()) {
              mylog(['La Lich Thu Goc Chinh' => 'Thuc hien sinh phi cham ky']);

							// Lich thu goc chinh & la hop dong trich ky -> thuc hien sinh phi ck
              if ($ra->collectDebtSummary->isHopDongSummaryTrichKy() && $plan->isLichHienTaiHoacQuaKhu()) {
                $phiChamKy = $plan->getSoTienPhiShared(CollectDebtEnum::GUIDE_LOAI_PHI_CHAM_KY, $soTienConPhaiThuTiep, $collectDebtShare);
								mylog(['PhiCK' => $phiChamKy]);

                $lichThuPhiChamKy = app(TaoLichThuPhiChamKyMLLTask::class)->run($plan, $phiChamKy, $collectDebtShare);
								mylog(['ID Lich thu phi ck' => $lichThuPhiChamKy->id]);

                $this->metaData[] = ['label' => CollectDebtEnum::METADATA_PHI_CHAM_KY, 'value' => $phiChamKy];
              }
            }
          } // End k phai lich tat toan


					// Lich tat toan
          if ($plan->isLichTatToan()) {
						mylog(['La lich thu TAT TOAN' => 'yes']);

            $isCoLichThuPhiQuaHan = app(IsCoLichThuPhiQuaHanTask::class)->run($plan->contract_code, true);
						mylog(['Is hop dong dang co lich thu phi qua han: ' => $isCoLichThuPhiQuaHan ? 'CO ROI' : 'CHUA CO']);

						// Tao lich thu vet goc tat toan
						$lichThuVetGocTatToan = app(TaoLichThuVetNoGocMLLTask::class)->run($plan, $soTienConPhaiThuTiep);
						$this->listLichThuDuocSinhRa->push($lichThuVetGocTatToan);

						mylog(['ID lich thu vet goc tat toan la' => $lichThuVetGocTatToan->id]);

            if ( !$plan->isLichTuongLai() ) {
							$isHomNayDaSinhPhiQuaHan = app(CheckLichMoLaiDaSinhPhiQuaHanMLLTask::class)->run($plan);
							mylog(['isHomNayDaSinhPhiQuaHan' => $isHomNayDaSinhPhiQuaHan]);

							// DK sinh phi: lich thu chinh hoac hom nay chua sinh phi qua han
              if ($plan->isLichThuChinh() || !$isHomNayDaSinhPhiQuaHan) {
                $phiQuaHan = $plan->getSoTienPhiShared(CollectDebtEnum::GUIDE_LOAI_PHI_QUA_HAN, $soTienConPhaiThuTiep, $collectDebtShare);

                $this->metaData[] = ['label' => CollectDebtEnum::METADATA_PHI_QUA_HAN, 'value' => $phiQuaHan];
  
                if (!$isCoLichThuPhiQuaHan) {
                  $lichThuPhiQuaHan = app(TaoLichThuPhiQuaHanMLLTask::class)->run($plan, $phiQuaHan, $collectDebtShare);
                }
  
                if ($isCoLichThuPhiQuaHan) {
                  $lichThuPhiQuaHanDaCongGopTien = app(CongTienQuaHanTask::class)->run(
                    $isCoLichThuPhiQuaHan,
                    time(),
                    now()->setTime(22, 0)->timestamp,
                    date('Ymd'),
                    $phiQuaHan
                  );
                }
              } // Chỉ sinh phí cho lịch hiện tại hoặc QK
            }
          } // End if is lich tat toan
        }

        // Đối với lịch thu phí thì chỉ cần tạo lịch thu vét nốt số tiền thu phí
        if ($plan->isLichThuPhi()) {
					mylog(['la lich thu phi' => 'yes']);

          if ($plan->isLichThuPhiChamKy()) {
						mylog(['la lich thu phi cham ky' => 'yes']);
            $this->metaData[] = ['label' => CollectDebtEnum::METADATA_THANH_TOAN_PHI_CK, 'value' => $soTienGhiNhanThanhCongChoLich];
            $lichThuPhiVet = app(TaoLichThuVetPhiConLaiMLLTask::class)->run($plan, $soTienConPhaiThuTiep, $collectDebtShare);
						$this->listLichThuDuocSinhRa->push($lichThuPhiVet);

            throw_if(!$lichThuPhiVet , new Exception('Loi khong tao dc lich thu vet MLL - CK'));
          }

          if ($plan->isLichThuPhiQuaHan()) {
						mylog(['la lich thu phi qua han' => 'yes']);

            $this->metaData[] = ['label' => CollectDebtEnum::METADATA_THANH_TOAN_PHI_QH, 'value' => $soTienGhiNhanThanhCongChoLich];
            $isCoLichThuPhiQuaHan = app(IsCoLichThuPhiQuaHanTask::class)->run($plan->contract_code, true);

            if ( !$isCoLichThuPhiQuaHan ) {
              $lichThuPhiVet = app(TaoLichThuVetPhiConLaiMLLTask::class)->run($plan, $soTienConPhaiThuTiep, $collectDebtShare);
							$this->listLichThuDuocSinhRa->push($lichThuPhiVet);
              throw_if(!$lichThuPhiVet , new Exception('Loi khong tao dc lich thu vet MLL - QH'));
            }
      
            if ( $isCoLichThuPhiQuaHan ) {
              $lichThuPhiQuaHanDaCongGopTien = app(CongTienQuaHanTask::class)->run(
                $isCoLichThuPhiQuaHan,
                time(),
                now()->setTime(22, 0)->timestamp,
                date('Ymd'),
                $soTienConPhaiThuTiep
              );
            }
          }
          
        }

				mylog(['meta' => $this->metaData]);
        // Cập nhật số tiền điều chỉnh vào trong bảng plan
        app(UpdateDieuChinhOtherDataPlanMLLTask::class)->run($plan, $soTienGhiNhanThanhCongChoLich, $soTienThuThanhCongTruocDo);
      }
    });

    return [
			'meta' => $this->metaData,
			'listLichThuDuocSinhRa' => $this->listLichThuDuocSinhRa
		];
  }
}
