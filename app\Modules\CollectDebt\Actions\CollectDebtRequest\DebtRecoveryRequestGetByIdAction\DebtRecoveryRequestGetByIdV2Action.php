<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestGetByIdAction;

use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Model\CollectDebtRequest;

class DebtRecoveryRequestGetByIdV2Action
{
  public function run(int $id): CollectDebtRequest
  {
    $rq = CollectDebtRequest::query()
						->join('debt_recovery_contract_guide', 'debt_recovery_contract_guide.contract_code', '=', 'debt_recovery_request.contract_code')
						->where('debt_recovery_request.id', $id)
						->select([
							'debt_recovery_request.id',
							'debt_recovery_request.contract_code',
							'debt_recovery_request.payment_method_code',
							'debt_recovery_request.partner_transaction_id',
							'debt_recovery_request.amount_request',
							'debt_recovery_request.amount_receiver',
							'debt_recovery_request.status',
							'debt_recovery_request.description',
							'debt_recovery_request.other_data',
							'debt_recovery_request.time_created',
							'debt_recovery_request.currency',
							'debt_recovery_request.create_from',

							'debt_recovery_contract_guide.contract_time_start',
							'debt_recovery_contract_guide.contract_time_end',
							'debt_recovery_contract_guide.time_created',
						])
						->first();
		
		return $rq;
  }
} // End class