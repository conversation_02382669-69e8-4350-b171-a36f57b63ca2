<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\CommonSubAction\Task;

use App\Lib\Helper;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestCreateManualRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\CommonSubAction\Task\FillPlanTask;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\CommonSubAction\Task\FillOtherTask;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class TaoYeuCauTrichTayTask
{
  public function run (
    Collection $danhSachToanBoLichThu, 
    CollectDebtShare $collectDebtShare, 
    DebtRecoveryRequestCreateManualRequest $request,
    string $paymentMethodCode='MPOS'
  ): CollectDebtRequest {
    switch ($paymentMethodCode) {
      case 'MPOS': 
        return $this->createYeuCauKhongChungTu(
          $danhSachToanBoLichThu,
          $collectDebtShare,
          $request,
          $paymentMethodCode
        );
        break;

      case 'IB_OFF': 
        return $this->createYeuCauCoChungTu(
          $danhSachToanBoLichThu,
          $collectDebtShare,
          $request,
          $paymentMethodCode
        );
        break;

      case 'VIRTUALACCOUNT': 
        return $this->createYeuCauCoChungTu(
          $danhSachToanBoLichThu,
          $collectDebtShare,
          $request,
          $paymentMethodCode
        );
        break;

      default: 
        throw new Exception('Unknow payment method code');
    }
  }

  public function createYeuCauCoChungTu(
    Collection $danhSachToanBoLichThu, 
    CollectDebtShare $collectDebtShare, 
    DebtRecoveryRequestCreateManualRequest $request,
    string $paymentMethodCode='MPOS'
  ) {
    $paymentGuide = $collectDebtShare->getPaymentGuideItem($paymentMethodCode);
    $fillPlans = app(FillPlanTask::class)->run($danhSachToanBoLichThu, $request);
    $fillOther = app(FillOtherTask::class)->run($request);

    $param = [
      'type'                             => CollectDebtEnum::REQUEST_TYPE_THANH_TOAN_TRICH_NO,
      'profile_id'                       => $collectDebtShare->profile_id,
      'contract_code'                    => $collectDebtShare->contract_code,
      'plan_ids'                         => $fillPlans['plan_ids'], 
      'payment_method_code'              => $paymentGuide['payment_method_code'],
      'payment_channel_code'             => $paymentGuide['payment_channel_code'],
      'payment_account_id'               => $paymentGuide['payment_account_id'],
      'partner_transaction_id'           => $request->json('data.partner_transaction_id'), 
      'currency'                         => $collectDebtShare->getCurrencyShared(),
      'amount_request'                   => $fillPlans['total_amount_request'],
      'amount_receiver'                  => $fillPlans['total_amount_receiver'],
      'time_begin'                       => now()->timestamp,
      'time_expired'                     => now()->setTime(22, 0)->timestamp,
      'is_payment'                       => CollectDebtEnum::REQUEST_IS_PAYMENT_KHONG_GUI_DOI_TAC_TT,
      'description'                      => $request->json('data.description'),
      'other_data'                       => $fillOther,
      'plan_data'                        => json_encode([]), 
      'version'                          => CollectDebtEnum::REQUEST_VERSION_4,
      'created_by'                       => $request->json('data.created_by'),
      'time_created'                     => time(),
      'create_from'                      => CollectDebtEnum::REQUEST_LOAI_TRICH_TAY,
      'status_payment'                   => CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA
    ];

    $collectDebtRequest = CollectDebtRequest::forceCreate($param);
    $collectDebtRequest->partner_request_id = sprintf('NL%s%s', date('ymd'), $collectDebtRequest->id);
    $collectDebtRequest->save();
  
    return $collectDebtRequest;
  }

  /**
   * Tạo yêu cầu mà không có chứng từ, dành riêng cho kênh MPOS
   *
   * @return void
   */
  public function createYeuCauKhongChungTu(
    Collection $danhSachToanBoLichThu, 
    CollectDebtShare $collectDebtShare, 
    DebtRecoveryRequestCreateManualRequest $request,
    string $paymentMethodCode='MPOS'
  ) {
    $paymentGuide = $collectDebtShare->getPaymentGuideItem($paymentMethodCode);
    $fillPlans = app(FillPlanTask::class)->run($danhSachToanBoLichThu, $request);
    $fillOther = app(FillOtherTask::class)->run($request);

    $status = CollectDebtEnum::REQUEST_STT_MOI_TAO;
    $status_payment = CollectDebtEnum::REQUEST_STT_PM_CHUA_GUI;
    $amountReceiver = 0;
    $expireTime = now()->setTime(22, 0)->timestamp;
    
    

    // Trích ngay thì ghi nhận kết quả luôn
    if ($request->isTrichNgay()) {
      $status = CollectDebtEnum::REQUEST_STT_MOI_TAO;
      $status_payment = CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA;
			
			// Nếu thời điểm tạo yêu cầu là thứ 7: thì thời điểm hết hạn của yc sẽ là 12:00 ngày thứ 2 gần nhất
			if (now()->isSaturday()) {
				$expireTime = now()->next('Monday')->setTime(12, 0)->timestamp;
			}else {
				// Còn không thì sẽ add thêm 01 ngày. Chắc chắn sẽ ko có lệnh nào exprired vào chủ nhật
				$expireTime = now()->addDay()->setTime(12, 0)->timestamp;
			}
      
			$amountReceiver = $request->json('data.amount_request'); // Sét số tiền thành công = Số tiền nhập vào ngay lập tức nếu là trích ngay
    }

    $param = [
      'type'                             => CollectDebtEnum::REQUEST_TYPE_THANH_TOAN_TRICH_NO,
      'profile_id'                       => $collectDebtShare->profile_id,
      'contract_code'                    => $collectDebtShare->contract_code,
      'plan_ids'                         => $fillPlans['plan_ids'], 
      'payment_method_code'              => $paymentGuide['payment_method_code'],
      'payment_channel_code'             => $paymentGuide['payment_channel_code'],
      'payment_account_id'               => $paymentGuide['payment_account_id'],
      'partner_transaction_id'           => $request->json('data.partner_transaction_id') . '_MANUAL_' . date('ymdHis'), // Lấy từ form
      'currency'                         => $collectDebtShare->getCurrencyShared(),
      'amount_request'                   => $request->json('data.amount_request'), // Số tiền yêu cầu MPOS trích là số tiền nhập vào
      'amount_receiver'                  => $amountReceiver,
      'time_begin'                       => now()->timestamp,
      'time_expired'                     => $expireTime,
      'is_payment'                       => CollectDebtEnum::REQUEST_IS_PAYMENT_CO_GUI_DOI_TAC_TT,
      'description'                      => $request->json('data.description'),
      'other_data'                       => $fillOther,
      'plan_data'                        => json_encode([]), 
      'version'                          => CollectDebtEnum::REQUEST_VERSION_4,
      'created_by'                       => $request->json('data.created_by'),
      'time_created'                     => time(),
      'create_from'                      => CollectDebtEnum::REQUEST_LOAI_TRICH_TAY,
      'status' => $status,
      'status_payment' => $status_payment,
    ];

    $collectDebtRequest = CollectDebtRequest::forceCreate($param);
    $collectDebtRequest->partner_request_id = sprintf('NL%s%s', date('ymd'), $collectDebtRequest->id);
    $collectDebtRequest->save();
    
    return $collectDebtRequest;
  }
} // End class