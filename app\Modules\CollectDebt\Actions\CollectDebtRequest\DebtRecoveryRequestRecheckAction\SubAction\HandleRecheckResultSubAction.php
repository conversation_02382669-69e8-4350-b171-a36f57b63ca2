<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestRecheckAction\SubAction;

use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCreateAction\SubAction\TaoCongNoSubAction;

class HandleRecheckResultSubAction
{
	public function run($mposCheckResult = [], CollectDebtRequest $collectDebtRequest)
	{
		$mposStatus = $mposCheckResult['data']['status'];
		$soTienTrichThanhCong = 0;

		if ($mposStatus == 'SUCCESS') {
			$soTienTrichThanhCong = !empty($mposCheckResult['data']['amount_debit']) ? $mposCheckResult['data']['amount_debit'] : 0;
		}

		if ($mposStatus == 'APPROVED') {
			$soTienTrichThanhCong = !empty($mposCheckResult['data']['amount_debit']) ? $mposCheckResult['data']['amount_debit'] : 0;
		}

		if ($mposStatus == 'EXPIRED') {
			$soTienTrichThanhCong = 0;
		}

		if ($mposStatus == 'CANCEL') {
			$soTienTrichThanhCong = 0;
		}

		// Tính từ thời điểm time_expired + 1h mà gọi sang check bị TIMEOUT thì ghi nhận 0đ luôn
		if ($mposStatus == 'TIMEOUT' && $collectDebtRequest->time_expired + 3600 < now()->timestamp) {
			$soTienTrichThanhCong = 0;
		}else {
			if (!in_array($mposStatus, ['SUCCESS', 'APPROVED', 'EXPIRED', 'CANCEL'])) {
				return $collectDebtRequest->partner_request_id;
			}
		}

		/**
		 * Recheck MPOS, thì payment_account_id phải là MÃ HỢP ĐỒNG
		 */
		if ($soTienTrichThanhCong > 0) {
			$request = request();
			$request->merge([
				'data' => [
					'payment_channel_code'   => $collectDebtRequest->payment_channel_code,
					'payment_method_code'    => $collectDebtRequest->payment_method_code,
					'payment_account_id'     => $collectDebtRequest->contract_code,
					'partner_request_id'     => $collectDebtRequest->partner_request_id,
					'partner_transaction_id' => $collectDebtRequest->partner_transaction_id,
					'amount_payment'         => 0,
					'amount_receiver'        => $soTienTrichThanhCong,
					'fee'                    => 0,
					'request_exists'         => CollectDebtEnum::PARTNER_REQUEST_DA_CO_YC_THANH_TOAN,
					'response'               => '[]',
					'description'            => 'Luồng Nextlend chủ động kiểm tra yêu cầu ReCheck',

					// các loại time recheck
					'created_by'             => Helper::getCronJobUser(),
					'time_created'           => time(),

					'time_updated'					 => time(),
					'updated_by'						 => Helper::getCronJobUser(),

					'time_complated'				 => time(),
					'complated_by'					 => Helper::getCronJobUser(),

					'time_created_request'   => time(),
					'created_request_by'		 => Helper::getCronJobUser(),

					'status'                 => CollectDebtEnum::PARTNER_STT_CHUA_XU_LY,
				]
			]);

			$collectDebtPartner = app(TaoCongNoSubAction::class)->run($collectDebtRequest, $request, true);
			if (!$collectDebtPartner) {
				$msg = "RECHECK ---> $collectDebtRequest->partner_request_id bị lỗi";
				throw new \Exception($msg);
			}
		}

		$collectDebtRequest->forceFill(['status' => CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH, 'time_updated' => time()])->update();
		return $collectDebtRequest->partner_request_id;
	}
}
