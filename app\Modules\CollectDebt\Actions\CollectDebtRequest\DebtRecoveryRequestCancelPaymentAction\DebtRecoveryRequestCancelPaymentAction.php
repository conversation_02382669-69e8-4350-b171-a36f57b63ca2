<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCancelPaymentAction;

use App\Lib\Helper;
use App\Lib\ApiCall;
use App\Utils\CommonVar;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSetting;

class DebtRecoveryRequestCancelPaymentAction
{
  /**
   * UPDATE 16.02.2024, không được ghi công nợ khi hủy yc tự động, mà v4 phải tự làm
   *
   * @param CollectDebtRequest $collectDebtRequest
   * @return void
   */
  public function run(CollectDebtRequest $collectDebtRequest)
  {
    $params = [
      'partner_request_id'     => $collectDebtRequest->partner_request_id,
      'partner_merchant_id'    => $collectDebtRequest->getPaymentAccountId(),
      'partner_transaction_id' => $collectDebtRequest->partner_transaction_id,
      'request_id'             => $collectDebtRequest->id,
      'users_admin_id'         => request('data.users_admin_id', Helper::getCronJobUser()),
      'write_note'             => 0, // 1: Cho phép ghi công nợ | 0: Không được phép ghi không nợ
      'payment_channel'        => 'MPOS',
      'contract_code'          => $collectDebtRequest->contract_code
    ];

    
    $payload = [
      'module' => CommonVar::API_REQUEST_DEBT_MODULE,
      'path' => '/partner-collect-debt-gateway/cancel-debt',
      'params' => $params,
      'method' => 'POST'
    ];

    $checkDebtResult = (new ApiCall())->callFunctionApi($payload, true);
    
    return $checkDebtResult;
  }
}  // End class