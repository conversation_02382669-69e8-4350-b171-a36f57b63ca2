<?php

namespace App\Modules\CollectDebt\Model;

use Illuminate\Database\Eloquent\Model;

class CollectDebtGenFee extends Model
{
	protected $table   = 'debt_recovery_gen_fee';
	public $timestamps = false;
	protected $appends = [];
	protected $guarded = [];

	public function collectDebtShareOnly() {
		return $this->belongsTo(CollectDebtShareOnly::class, 'contract_code', 'contract_code');
	}
} // End class
