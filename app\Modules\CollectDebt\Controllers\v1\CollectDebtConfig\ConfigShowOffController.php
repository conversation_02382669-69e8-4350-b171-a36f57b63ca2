<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtConfig;

use Illuminate\Http\Request;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Actions\CollectDebtConfig\ConfigButtonShowOffAction;

class ConfigShowOffController extends Controller
{
	public function index(Request $request)
	{
		$contractCode = trim($request->json('data.contract_code', ''));
		$collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $contractCode)->first();
		$buttonState = app(ConfigButtonShowOffAction::class)->run($collectDebtSummary);
		return $this->successResponse($buttonState, $request);
	}
} // End class
