<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCheckLedgerExsistAction;

use Exception;
use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use App\Lib\NextlendCore;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateSendAction\Task\GetHopDongNhanTienTuNguonThuThuaTask;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;

class SyncContractBatchAction extends BatchProcessingAction
{
	public NextlendCore $nextlendCore;

	public function __construct(NextlendCore $nextlendCore)
	{
		$this->nextlendCore = $nextlendCore;
	}
	
	public function run()
	{
		$startTime = microtime(true);

		// $client = $this->createHttpClient();

		CollectDebtSummary::query()
			->where('is_request_sync', CollectDebtEnum::SUMMARY_CO_DONG_BO)
			->select(['contract_code'])
			->chunkById(self::CHUNK_LIMIT, function (Collection $listSummary) use ($startTime) {

				if ((microtime(true) - $startTime) > self::BATCH_LIMIT_IN_SECONDS) {
					return false; 
				}

				$this->processBatchSync($listSummary);

				unset($listSummary);
			});

		return [
			'done' => $this->done,
			'failed' => $this->failed,
		];
	}

	private function processBatchSync(Collection $collectDebtSummaries): void
	{
		foreach ($collectDebtSummaries as $summary) {
			$this->handleSyncContract($summary->contract_code);
		}
	}

	// private function processBatch(Client $client, Collection $collectDebtSummaries): void
	// {
	// 	// Generator
	// 	$requests = function () use ($collectDebtSummaries) {
	// 		foreach ($collectDebtSummaries as $summary) {
	// 			$url = config('app.url') . '/HandleSyncContract/' . $summary->contract_code;
	// 			yield $summary->contract_code => new Request('POST', $url, ['Content-Type' => 'application/json']);
	// 		}
	// 	};

	// 	$pool = new Pool($client, $requests(), [
	// 		'concurrency' => self::POOL_CONCURRENCY,
	// 		'fulfilled' => function ($response, string $contractCode) {
	// 			// $body = (string)$response->getBody();
	// 			$this->done++;
	// 		},
	// 		'rejected' => function (\Throwable $reason, string $contractCode) {
	// 			$msg = "[DongBo --->$contractCode] failed: " . $reason->getMessage();
	// 			Log::info($msg);
	// 			$this->failed++;
	// 		},
	// 	]);

	// 	$promise = $pool->promise();
	// 	$promise->wait();
	// }

	public function handleSyncContract(string $contractCode): string
	{
		$collectDebtSummary = CollectDebtSummary::query()->firstWhere(['contract_code' => $contractCode]);

		throw_if(!$collectDebtSummary, new Exception('Contract not found'));

		if ($collectDebtSummary->isHopDongTest()) {
			return $this->__markAsSynced($collectDebtSummary->toArray());
		}

		$calcTime = app(DongBoVeHeThongHopDongAction::class)->__caculateTime($collectDebtSummary);
		$collectDebtSummary->number_day_status_debt = $calcTime['number_day_status_debt'];
		$collectDebtSummary->next_payment_period = $calcTime['next_payment_period'];

		$input = $collectDebtSummary->makeHidden(['other_data'])->toArray();
		
		// Xử lý dùng tiền thừa thanh toán cho HĐ khác
		$input['excess_handler'] = [
			'refund' => [
				'amount_refund' => $collectDebtSummary->total_amount_excess_refund
			],
			'repayment' => [
				'amount_repayment' => $collectDebtSummary->total_amount_repayment_debt,
				'list_contract' =>  []
			]
		];

		if ( !empty($collectDebtSummary->total_amount_repayment_debt) ) {
			$listHopDongNhanTien = app(GetHopDongNhanTienTuNguonThuThuaTask::class)->run($collectDebtSummary->contract_code);
			if (isset($listHopDongNhanTien['data']['listMaHopDongNhanTienCashIn'])) {
				$input['excess_handler']['repayment']['list_contract'] = $listHopDongNhanTien['data']['listMaHopDongNhanTienCashIn'];
			}
		}
		
		$nextlend = $this->nextlendCore->callRequest($input, 'ContractV4_summaryData', 'POST');
		$result = $nextlend->decryptData();
		if ( empty($result['contract_id']) ) {
			throw new Exception('Dong bo hop dong bi loi');
		}

		return $this->__markAsSynced($input);
	}

	private function __markAsSynced($input): string {
		$currntTimestamp = now()->timestamp;

		CollectDebtSummary::query()->where('contract_code', $input['contract_code'])->update([
			'is_request_sync' => CollectDebtEnum::SUMMARY_KHONG_DONG_BO,
			'time_updated' => now()->timestamp,
			'time_complated' => now()->timestamp
		]);

		//unset($input['other_data']);
		
		CollectDebtContractEvent::query()->updateOrCreate([
			'contract_code' => $input['contract_code'],
			'category_care_code' => 'CONTRACT',
			'service_care_code' => 'SYNC',
		], [
			'data' => json_encode([]),
			'description' => 'Đông bộ contract',
			'content' => '',
			'other_data' => json_encode([
				'summary' => $input
			]),
			'status' => 5,
			'time_start' => $currntTimestamp,
			'number' => 1,
			'time_expired' => $currntTimestamp,
			'time_sented' => $currntTimestamp,
			'time_created_content' => $currntTimestamp,
			'time_updated' => $currntTimestamp,
			'time_created' => $currntTimestamp,
		]);

		return $input['contract_code'];
	}
} // End class
