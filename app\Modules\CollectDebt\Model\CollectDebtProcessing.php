<?php

namespace App\Modules\CollectDebt\Model;

use Illuminate\Database\Eloquent\Model;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtRequestOnly;

class CollectDebtProcessing extends Model
{
	protected $table = 'debt_recovery_processing';
	protected $guarded = [];

	protected $casts = [
		'expired_at' => 'datetime',
		'checked_at' => 'datetime',
		'created_at' => 'datetime',
		'updated_at' => 'datetime',
	];

	public function collectDebtRequestOnly()
	{
		return $this->hasOne(CollectDebtRequestOnly::class, 'partner_request_id', 'partner_request_id');
	}

	public function collectDebtRequest()
	{
		return $this->hasOne(CollectDebtRequest::class, 'partner_request_id', 'partner_request_id');
	}
} // End class
