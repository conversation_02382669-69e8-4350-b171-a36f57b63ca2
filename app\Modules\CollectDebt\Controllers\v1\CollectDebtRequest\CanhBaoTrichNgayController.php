<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest;

use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\DebtNowEnum;
use App\Modules\CollectDebt\Model\CollectDebtLog;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest\CollectDebtRequestRecheckController;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestRecheckAction\XuLyRecheckChoLenhTrichNgaySubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\CheckRequestViaMposSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtNotifyDebtNow\CollectDebtNotifyDebtNowCreateAction\CollectDebtNotifyDebtNowCreateAction;

class CanhBaoTrichNgayController extends Controller
{
	public array $listLenhTrichDaCanhBao = [];

	public array $trangThaiThanhCong = ['SUCCESS', 'APPROVE', 'APPROVED'];

	public function isThanhCong($mposResult = ''): bool
	{
		return in_array($mposResult, $this->trangThaiThanhCong);
	}

	public function getListYcTrichNgay()
	{
		$listLogTrichNgayCanKiemTra = CollectDebtLog::query()->where([
			['service_code', '=', CollectDebtEnum::RL_DICH_VU_KIEM_TRA_TRICH_NGAY],
			['status', '=', CollectDebtEnum::RL_STT_MOI_TAO],
			['time_expired', '<', now()->timestamp],
		])
			->select(['id', 'partner_transaction_id', 'status'])
			->get();

		return $listLogTrichNgayCanKiemTra;
	}

	public function getListLenhTrichNgay(Collection $listLogTrichNgayCanKiemTra)
	{
		$listPartnerTransactionId = $listLogTrichNgayCanKiemTra->pluck('partner_transaction_id')->toArray();

		$listRequest = CollectDebtRequest::query()->whereIn('partner_transaction_id', $listPartnerTransactionId)->get();
		return $listRequest;
	}

	public function handler(Request $request)
	{
		$listLog = $this->getListYcTrichNgay();

		if ($listLog->isEmpty()) {
			return ['msg' => 'no request need handle'];
		}


		$listYcTrichNgay = $this->getListLenhTrichNgay($listLog);

		if ($listYcTrichNgay->isEmpty()) {
			return ['msg' => 'no request need handle'];
		}

		foreach ($listYcTrichNgay as $collectDebtRequest) {

			$checkMposResult = app(CheckRequestViaMposSubAction::class)->run($collectDebtRequest, $request, true);

			$soTienThanhCong = 0;
			$isThanhCong = $this->isThanhCong(@$checkMposResult['data']['status']);

			if ($isThanhCong) {
				$soTienThanhCong = $checkMposResult['data']['amount_debit'];
			}

			$subject = sprintf(
				'[%s] - Cảnh báo lệnh trích ngay: %s đang không thu hồi đủ tiền. Trạng thái: %s',
				$collectDebtRequest->contract_code,
				$collectDebtRequest->partner_request_id,
				$checkMposResult['data']['status'] ?? 'EMPTY'
			);


			$params = [
				'contract_code' => $collectDebtRequest->contract_code,
				'to' => '<EMAIL>',
				'cc' => '<EMAIL>',
				'sender' => '',
				'subject' => $subject,
				'description' => '',
				'other_data' => [
					'MaLenhTrich' => $collectDebtRequest->partner_request_id,
					'MaHd' => $collectDebtRequest->contract_code,
					'TrangThai' => $checkMposResult['data']['status'] ?? 'EMPTY',
					'SoTienCanPhaiThu' => Helper::priceFormat($collectDebtRequest->amount_request),
					'SoTienDaThuDuocThucTe' => Helper::priceFormat($soTienThanhCong)
				],
				'request_id' => $collectDebtRequest->id
			];

			if ($soTienThanhCong == $collectDebtRequest->amount_request) {
				$params['status'] = DebtNowEnum::STT_DA_XU_LY;
			}

			$collectDebtNotifyDebtNow = app(CollectDebtNotifyDebtNowCreateAction::class)->run($params);

			if ($collectDebtNotifyDebtNow) {
				CollectDebtLog::query()
					->where('partner_transaction_id', $collectDebtRequest->partner_transaction_id)
					->where('service_code', CollectDebtEnum::RL_DICH_VU_KIEM_TRA_TRICH_NGAY)
					->update([
						'status' => CollectDebtEnum::RL_STT_DA_XU_LY_THANH_CONG,
						'time_updated' => now()->timestamp,
					]);
			}

			$msg = sprintf('env: %s --- subject: %s', config('app.env'), $subject);
			@TelegramAlert::alertKiemTraTrichNgay($msg);

			$this->listLenhTrichDaCanhBao[] = $collectDebtRequest->only([
				'partner_request_id',
				'contract_code'
			]);

			$this->XuLyMoLaiLich($checkMposResult, $collectDebtRequest);
		} // End foreach

		return response()->json([
			'listLenhTrichDaXuLy' => $this->listLenhTrichDaCanhBao
		], 200);
	}

	public function XuLyMoLaiLich($checkMposResult, $collectDebtRequest)
	{
		$collectDebtRequestAfterHandler = app(XuLyRecheckChoLenhTrichNgaySubAction::class)->run($checkMposResult, $collectDebtRequest);

		app(CollectDebtRequestRecheckController::class)->xuLyBanGhiLog($collectDebtRequestAfterHandler, $checkMposResult);

		return $collectDebtRequestAfterHandler;
	}
} // End class
