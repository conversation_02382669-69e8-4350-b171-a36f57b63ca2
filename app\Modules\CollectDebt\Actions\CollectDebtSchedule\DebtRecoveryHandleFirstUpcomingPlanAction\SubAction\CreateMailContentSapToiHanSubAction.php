<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleFirstUpcomingPlanAction\SubAction;

use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;

class CreateMailContentSapToiHanSubAction
{
	public function run(CollectDebtSchedule $plan, $categoryCareCode='NOTIFY_CONTRACT_DUE')
	{
		$companyData = json_decode($plan->collectDebtShare->other_data, true);
		$profileData = json_decode($plan->collectDebtShare->profile_data, true);
		$contractData = json_decode($plan->collectDebtShare->contract_data, true);
		$payment = json_decode($plan->collectDebtShare->payment_guide, true);
		
		$param = [
			'contract_code' => $plan->contract_code,
			'category_care_code' => $categoryCareCode,
			'service_care_code' => 'MAIL',
			'data' => json_encode([
				'company' => $companyData['company']['data'],
				'profile' => $profileData,
				'contract' => $contractData,
				'payment' => $payment
			], JSON_UNESCAPED_UNICODE),
			'description' => '',
			'content' => null,
			'other_data' => json_encode([
				'summary' => $plan->collectDebtSummary->toArray()
			], JSON_UNESCAPED_UNICODE),
			'status' => 1,
			'number' => 1,
			'time_created' => time(),
			'time_updated' => time()
		];

		
		$collectDebtContractEvent = CollectDebtContractEvent::query()->forceCreate($param);
		return $collectDebtContractEvent;
	}
} // End class