<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\GetListPlanByRequestIdAction;

use App\Modules\CollectDebt\Model\CollectDebtPlan;
use App\Modules\CollectDebt\Model\CollectDebtRequestOnly;

class GetListPlanByRequestIdAction
{
	public function run($id)
	{
		$collectDebtRequest = CollectDebtRequestOnly::query()->find($id, ['id', 'plan_ids', 'currency']);

		if (!$collectDebtRequest) {
			throw new \Exception('Không tìm thấy thông tin yêu cầu');
		}

		$planIds = explode(',', $collectDebtRequest->plan_ids);
		
		$plans = CollectDebtPlan::query()->whereIn('id', $planIds)
			->select([
				'id',
				'master_id',
				'profile_id',
				'contract_code',
				'contract_type',
				'type',
				'rundate',
				'time_start',
				'time_end',
				'amount_period_debit',
				'request_amount_debit',
				'success_amount_debit',
				'description',
				'is_settlement',
				'other_data',
				'status',
				'debit_begin',
				'debit_end',
				'cycle_number'
			])
			->get();

		$plans->transform(function (CollectDebtPlan $p) use ($collectDebtRequest) {
			$p->currency = $collectDebtRequest->currency;
			return $p;
		});

		return $plans;
	}
}  // End class