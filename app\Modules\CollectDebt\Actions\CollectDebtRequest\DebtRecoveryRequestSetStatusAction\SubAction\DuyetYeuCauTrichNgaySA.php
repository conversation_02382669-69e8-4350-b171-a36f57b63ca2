<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSetStatusAction\SubAction;

use Exception;
use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction\SendRequestViaMposSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\CommonSubAction\Task\TaoCongNoKhiLaTrichNgayTask;
use App\Modules\CollectDebt\Model\CollectDebtLog;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use PhpParser\Node\Expr\Throw_;

class DuyetYeuCauTrichNgaySA
{
  public function run(CollectDebtRequest $collectDebtRequest, $request): CollectDebtRequest
  {
    $collectDebtRequest->other_data = $collectDebtRequest->putOtherData([
      'type' => 'APPROVED',
      'time_modified' => time(),
      'note' => $request->json('data.description', 'Duyệt yêu cầu trích ngay'),
      'data' => [
        'user' => $request->json('data.user_request_id')
      ]
    ]);

    $collectDebtRequest->save();

    $rq = app(SendRequestViaMposSubAction::class)->run($collectDebtRequest);

    throw_if(!$rq->isSentPayment(), new Exception('Không thể gửi yêu cầu trích ngay qua đối tác MPOS'));

    $rq->forceFill([
      'status'         => CollectDebtEnum::REQUEST_STT_DA_DUYET,
      'status_payment' => CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA,
      'completed_by'   => $request->json('data.user_request_id'),
      'time_approved'  => time(),
      'approved_by'    => $request->json('data.user_request_id'),
      'time_completed' => time()
    ])->update();

    $rq->refresh();

    $partner = app(TaoCongNoKhiLaTrichNgayTask::class)->run($collectDebtRequest);
		
		if (!$partner) {
			mylog([
				'[LOI]' => 'Khong tao duoc partner',
				'chi tiet' => $partner
			]);

			throw new Exception('Khong tao duoc partner');
		}

		// Cap nhat lich la dang xu ly
		try {
			$planIds = $collectDebtRequest->getPlanIds();

			$updatedIsProcesVeDangXuLy = CollectDebtSchedule::query()
																											->where('contract_code', $collectDebtRequest->contract_code)
																											->whereIn('id', $planIds)
																											->where('status', '!=', CollectDebtEnum::SCHEDULE_STT_DA_HOAN_THANH)
																											->update([
																												'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY
																											]);
			if (!$updatedIsProcesVeDangXuLy) {
				// chi duoc ghi log, ko dc throw loi
				mylog([
					'[LOI]' => 'Cap nhat trang thai lich trich ngay ve DANG XU LY that bai',
					'chi tiet' => $updatedIsProcesVeDangXuLy
				]);
			}
		}catch(\Throwable $th) {
			mylog(['[Loi]' => Helper::traceError($th)]);
		}

		// Tao ban ghi log trich ngay de con lam recheck
		if (!empty($rq->partner_transaction_id)) {
			$collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $rq->contract_code)->first();

			$paramLog = [
				'service_code' => CollectDebtEnum::RL_DICH_VU_KIEM_TRA_TRICH_NGAY,
				'partner_transaction_id' => $rq->partner_transaction_id
			];

			$collectDebtLog = CollectDebtLog::query()->firstOrCreate($paramLog, [
				'reference_id' => optional($collectDebtSummary)->id,
				'order_code' => sprintf('DEBTNOW_%s', $collectDebtRequest->id),
				'time_created' => now()->timestamp,
				'created_by' => $request->json('data.user_request_id', Helper::getCronJobUser()),
				'time_expired' => $rq->time_expired,
			]);

			if (!$collectDebtLog) {
				mylog(['Loi khong tao duoc log' => $collectDebtLog]);
				throw new Exception('Loi khong tao duoc log');
			}
		}
		
    $rq->__apiMessage = 'Đã duyệt yêu cầu TRÍCH NGAY';
    return $rq;
  }
}