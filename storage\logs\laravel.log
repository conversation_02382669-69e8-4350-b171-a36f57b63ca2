[2025-09-03 17:14:30] local.INFO: CutOffRequestBatchAction {"request":{"api_request_id":"DEBT_09031714_ejSF1F"}} 
[2025-09-03 17:14:30] local.ERROR: A void function must not return a value {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): A void function must not return a value at C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestFinishCutOffTimeAction\\CutOffRequestBatchAction.php:81)
[stacktrace]
#0 {main}
"} 
[2025-09-03 17:14:48] local.INFO: CutOffRequestBatchAction {"request":{"api_request_id":"DEBT_09031714_91jqxn"}} 
[2025-09-03 17:14:48] local.ERROR: A void function must not return a value {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): A void function must not return a value at C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestFinishCutOffTimeAction\\CutOffRequestBatchAction.php:82)
[stacktrace]
#0 {main}
"} 
[2025-09-03 17:14:49] local.INFO: CutOffRequestBatchAction {"request":{"api_request_id":"DEBT_09031714_FCAY4n"}} 
[2025-09-03 17:14:49] local.ERROR: A void function must not return a value {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): A void function must not return a value at C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestFinishCutOffTimeAction\\CutOffRequestBatchAction.php:82)
[stacktrace]
#0 {main}
"} 
[2025-09-03 17:14:58] local.INFO: CutOffRequestBatchAction {"request":{"api_request_id":"DEBT_09031714_YZhhMz"}} 
[2025-09-03 17:15:00] local.INFO: CutOffRequestBatchAction {"request":{"api_request_id":"DEBT_09031715_aVNQB5"}} 
[2025-09-03 17:15:07] local.INFO: CutOffRequestBatchAction {"request":{"api_request_id":"DEBT_09031715_JO136C"}} 
[2025-09-03 17:15:38] local.INFO: CutOffRequestBatchAction {"request":{"api_request_id":"DEBT_09031715_qetUEC"}} 
[2025-09-03 17:15:39] local.INFO: HandleCutOffRequest {"request":{"min":"1944","max":"1973","api_request_id":"DEBT_09031715_UuY4Ju"}} 
[2025-09-03 17:15:39] local.INFO: HandleCutOffRequest {"request":{"min":"1974","max":"2003","api_request_id":"DEBT_09031715_gX1R2k"}} 
[2025-09-03 17:15:39] local.INFO: HandleCutOffRequest {"request":{"min":"2004","max":"2017","api_request_id":"DEBT_09031715_FsbXDl"}} 
[2025-09-03 17:16:01] local.INFO: HandleCutOffRequest {"request":{"min":"1944","max":"1973","api_request_id":"DEBT_09031716_c4HXVM"}} 
[2025-09-03 17:16:17] local.INFO: HandleCutOffRequest {"request":{"min":"1944","max":"1973","api_request_id":"DEBT_09031716_lPnq4n"}} 
[2025-09-03 17:19:17] local.INFO: HandleCutOffRequest {"request":{"min":"1944","max":"1973","api_request_id":"DEBT_09031719_EWClgl"}} 
