<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPlan;

class CanCreateRequestIfHaveMultipleSubAction
{
	public function run($profileId, string $currentContractCode): bool
	{
		/**
		 * Quan trọng: - Luôn đọc vào bảng Lịch thu để đảm bảo là HỢP ĐỒNG HÔM NAY
		 * 						 - WHERE để đảm bảo đúng trạng thái và đúng ngày vì `plans` đc tạo
		 * yêu cầu thì sẽ nhảy trạng thái --> đảm bảo lệnh trích đc tạo không bị miss
		 */
		$plans = CollectDebtPlan::query()
			->with('collectDebtShareOnly:contract_code,partner_code')
			->where("rundate", '<=', intval(date("Ymd")))
			->where("profile_id", $profileId)
			->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
			->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
			->select(['contract_code', 'rundate'])
			->get();

		if ($plans->isEmpty()) {
			return false;
		}

		$hasLichQuaKhu = $plans->contains(function ($p) {
			return $p->rundate < intval(date("Ymd"));
		});

		if ($hasLichQuaKhu) {
			return true;
		}

		$plans = $plans->unique("contract_code");

		if ($plans->count() == 1) {
			return true;
		}

		$plans = $plans->sort(function ($a, $b) {
			// So sánh partner_code trước
			if ($a->collectDebtShareOnly->partner_code !== $b->collectDebtShareOnly->partner_code) {
				// NEXTLEND lên trước, TNEX xuống dưới
				if ($a->collectDebtShareOnly->partner_code === "NEXTLEND") {
					return -1;
				}
				if ($b->collectDebtShareOnly->partner_code === "NEXTLEND") {
					return 1;
				}
				if ($a->collectDebtShareOnly->partner_code === "TNEX") {
					return 1;
				}
				if ($b->collectDebtShareOnly->partner_code === "TNEX") {
					return -1;
				}

				return strcmp($a->collectDebtShareOnly->partner_code, $b->collectDebtShareOnly->partner_code);
			}

			// Cùng partner_code thì so sánh theo số lần vay
			// Cắt chuỗi để lấy số sau -L
			$getLoanCount = function ($contractCode) {
				if (preg_match('/-L(\d+)$/', $contractCode, $matches)) {
					return (int) $matches[1];
				}
				return 0; // Nếu không có -L thì coi như 0
			};

			$loanCountA = $getLoanCount($a->contract_code);
			$loanCountB = $getLoanCount($b->contract_code);

			// Số lần vay nhỏ hơn lên trước
			return $loanCountA - $loanCountB;
		});

		$first = $plans->first();
		return $first->contract_code == $currentContractCode;
	}
} // End class