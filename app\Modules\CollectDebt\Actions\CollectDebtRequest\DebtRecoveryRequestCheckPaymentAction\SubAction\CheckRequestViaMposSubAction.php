<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction;

use App\Lib\Helper;
use App\Lib\ApiCall;
use App\Utils\CommonVar;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Model\CollectDebtPlan;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class CheckRequestViaMposSubAction
{
  /***
   * array:6 [▼
      "api_request_id" => null
      "success" => true
      "checksum" => "ac54fa01105d54298f52fa9cfa080d8d"
      "result_code" => 200
      "message" => "Request success"
      "data" => array:5 [▼
        "error_code" => "00"
        "description" => "SUCCESS"
        "status" => "SUCCESS"
        "amount_debit" => "0"
        "partner_transaction_id" => "38931752"
      ]
    ]
   *
   * @param CollectDebtRequest $collectDebtRequest [explicite description]
   * @param Request $request [explicite description]
   *
   * @return void
   */
  public function run(CollectDebtRequest $collectDebtRequest, Request $request, bool $isKhongTacDongVaoCongNo = false)
  {
    $params = $this->buildParams($collectDebtRequest, $request);

    if ($isKhongTacDongVaoCongNo) {
      $params['partner_save'] = 1;
    }

    $payload = [
      'module' => CommonVar::API_REQUEST_DEBT_MODULE,
      'path' => '/partner-collect-debt-gateway/check-debt',
      'params' => $params,
      'method' => 'POST'
    ];

    $checkDebtResult = (new ApiCall())->callFunctionApi($payload, true);

    $collectDebtRequest->time_checked = time();
    $collectDebtRequest->checked_by = Helper::getCronJobUser();

    if (!empty($checkDebtResult['data']['status']) && $checkDebtResult['data']['status'] == 'SUCCESS') {
      $collectDebtRequest->time_receivered = time();
    }


    
    $collectDebtRequest->save();
    return $checkDebtResult;
  }

	public function checkWithoutAnyAction(CollectDebtRequest $collectDebtRequest, Request $request, bool $isKhongTacDongVaoCongNo = false)
  {
    $params = $this->buildParams($collectDebtRequest, $request);

    if ($isKhongTacDongVaoCongNo) {
      $params['partner_save'] = 1;
    }

    $payload = [
      'module' => CommonVar::API_REQUEST_DEBT_MODULE,
      'path' => '/partner-collect-debt-gateway/check-debt',
      'params' => $params,
      'method' => 'POST'
    ];

    $checkDebtResult = (new ApiCall())->callFunctionApi($payload, true);
    return $checkDebtResult;
  }

  public function buildParams(CollectDebtRequest $collectDebtRequest, $request)
  {
    $collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $collectDebtRequest->contract_code)->first();
    $firstPlan = CollectDebtPlan::query()->where('contract_code', $collectDebtSummary->contract_code)
																				 ->orderBy('id', 'ASC')
																				 ->select(['amount_period_debit', 'debit_begin'])
																				 ->first();

    $params = [
      'partner_request_id'       => $collectDebtRequest->partner_request_id,
      'partner_merchant_id'      => $collectDebtRequest->getPaymentAccountId(), // Fake
      'amount_payment'           => $collectDebtRequest->amount_request,
      'contract_code'            => $collectDebtRequest->contract_code,
      'loan_original_amount'     => $collectDebtSummary->getSoTienGiaiNgan(), // Số  tiền giải ngân
      'deduction_per_day_amount' => $firstPlan->amount_period_debit,  // Số tiền trích nợ hằng ngày: Là số tiền chia đều cho các kỳ
      'loan_balance'             => $firstPlan->debit_begin, // Dư nợ đầu kỳ
      'request_id'               => $collectDebtRequest->id,
      'users_admin_id'           => $request->json('data.users_admin_id', Helper::getCronJobUser()),
      'payment_channel'          => 'MPOS',
      'amount'                   => $collectDebtRequest->amount_request,
    ];

    return $params;
  }

} // End class