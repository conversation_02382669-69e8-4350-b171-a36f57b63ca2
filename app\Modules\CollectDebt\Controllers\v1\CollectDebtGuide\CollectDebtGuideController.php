<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtGuide;

use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Resources\CollectDebtGuideResourceCollection;
use App\Modules\CollectDebt\Requests\v1\CollectDebtGuide\DebtRecoveryContractGuideGetById;
use App\Modules\CollectDebt\Requests\v1\CollectDebtGuide\DebtRecoveryContractGuideSetStatus;
use App\Modules\CollectDebt\Requests\v1\CollectDebtGuide\DebtRecoveryContractGuideCreateRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtGuide\DebtRecoveryContractGuideUpdateRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtGuide\DebtRecoveryContractGuideGetByContractCodeRequest;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideUpdateAction\DebtRecoveryContractGuideUpdateAction;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideGetByIdAction\DebtRecoveryContractGuideGetByIdAction;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideGetByRawAction\DebtRecoveryContractGuideGetByRawAction;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideSetStatusAction\DebtRecoveryContractGuideSetStatusAction;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideCreatePlanAction\DebtRecoveryContractGuideCreatePlanAction;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideSearchDataAction\DebtRecoveryContractGuideSearchDataAction;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideAutoApprovedAction\DebtRecoveryContractGuideAutoApprovedAction;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideQuickStatisticAction\DebtRecoveryContractGuideQuickStatisticAction;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideSyncPaymentGuideAction\DebtRecoveryContractGuideSyncPaymentGuideAction;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideGetByContractCodeAction\DebtRecoveryContractGuideGetByContractCodeAction;
use Arr;

class CollectDebtGuideController extends Controller
{
	
	public function index(Request $request)
	{
		try {
			$collectDebtGuids = app(DebtRecoveryContractGuideSearchDataAction::class)->run($request);
			$collectDebtGuidsResource = new CollectDebtGuideResourceCollection($collectDebtGuids);
			$response = $collectDebtGuidsResource->toResponse($request)->getData(true);
			return $this->successResponse($response, $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function store(DebtRecoveryContractGuideCreateRequest $request)
	{
		try {
			$collectDebtGuideExist = CollectDebtGuide::where('contract_code', $request->json('data.contract_code'))->first();
			
			if (!$collectDebtGuideExist) {
				$params = $request->only([
					'data.profile_id',
					'data.contract_code',
					'data.contract_type',
					'data.contract_cycle',
					'data.contract_intervals',
					'data.contract_time_start',
					'data.contract_time_end',
					'data.amount',
					'data.payment_guide',
					'data.list_fee',
					'data.other_data',
					'data.profile_data',
					'data.contract_data',
					'data.description',
					'data.status',
					'data.created_by',
					'data.updated_by',
					'data.approved_by',
					'data.canceled_by',
					'data.create_calendar_by',
					'data.time_created',
					'data.time_updated',
					'data.time_approved',
					'data.time_canceled',
					'data.time_create_calendar',
					'data.partner_code',
				])['data'];

				$collectDebtGuide = CollectDebtGuide::query()->forceCreate($params);
			}else {
				$collectDebtGuide = app(DebtRecoveryContractGuideUpdateAction::class)->run($collectDebtGuideExist, $request);
			}

			$collectDebtGuide->status = CollectDebtEnum::GUIDE_STT_MOI_TAO;
			$collectDebtGuide->save();

			$message = [
				'LogId' => $request->json('api_request_id'),
				'Message' => sprintf('Tạo chỉ dẫn HĐ: `%s` thành công', $collectDebtGuide->contract_code)
			];

			TelegramAlert::sendDayChiDan($message);
			return $this->successResponse(['id' => $collectDebtGuide->id], $request);
		}catch (\Throwable $th) {
			
			$message = [
				'Error' => $th->getMessage(),
				'LogId' => $request->json('api_request_id')
			];

			TelegramAlert::sendDayChiDan($message);
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function update(DebtRecoveryContractGuideUpdateRequest $request)
	{
		try {
			$collectDebtGuide = app(DebtRecoveryContractGuideGetByIdAction::class)->run($request->json('data.id'));
			$collectDebtGuideUpdate = app(DebtRecoveryContractGuideUpdateAction::class)->run($collectDebtGuide, $request);
			return $this->successResponse($collectDebtGuideUpdate->toArray(), $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}


	public function setStatus(DebtRecoveryContractGuideSetStatus $request)
	{
		try {
			$collectDebtGuide = app(DebtRecoveryContractGuideGetByIdAction::class)->run($request->json('data.id'));
			$collectDebtGuideSetStatus = app(DebtRecoveryContractGuideSetStatusAction::class)->run($collectDebtGuide, $request);

			return $this->successResponse($collectDebtGuideSetStatus->toArray(), $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}


	public function show(DebtRecoveryContractGuideGetById $request)
	{
		try {
			$collectDebtGuide = app(DebtRecoveryContractGuideGetByIdAction::class)->run($request->json('data.id'));
			return $this->successResponse($collectDebtGuide->toArray(), $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function getByContractCode(DebtRecoveryContractGuideGetByContractCodeRequest $request)
	{
		try {
			$r = app(DebtRecoveryContractGuideGetByContractCodeAction::class)->run($request->json('data.contract_code'));
			return $this->successResponse($r, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
	
	// Tạo lịch theo chỉ dẫn
	public function createPlan(Request $request)
	{
		DB::beginTransaction();
		try {
			$statements = [
				'where' => sprintf(' status = %s ', CollectDebtEnum::GUIDE_STT_DA_DUYET ),
				'orderBy' => ' id ASC ',
				'limit' => 1
			];

			$collectDebtGuideCollection = app(DebtRecoveryContractGuideGetByRawAction::class)->run($statements);
			$collectDebtScheduleCollection = app(DebtRecoveryContractGuideCreatePlanAction::class)->run($collectDebtGuideCollection);
			DB::commit();

			$returnData = [];
			
			if ($collectDebtScheduleCollection) {
				$returnData = $collectDebtScheduleCollection->pluck('id')->toArray();
			}

			return $this->successResponse($returnData, $request);
		} catch (\Throwable $th) {
			DB::rollBack();
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	
	public function quickStatistic(Request $request)
	{
		try {
			$result = app(DebtRecoveryContractGuideQuickStatisticAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
	
	public function autoAprroved(Request $request)
	{
		try {
			$collectDebtGuide = app(DebtRecoveryContractGuideAutoApprovedAction::class)->run($request);
			return $this->successResponse($collectDebtGuide, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function syncPaymentGuide(Request $request)
	{
		DB::beginTransaction();
		try {
			$collectDebtGuide = app(DebtRecoveryContractGuideSyncPaymentGuideAction::class)->run($request);
			DB::commit();
			return $this->successResponse([
				'id' => $collectDebtGuide->id 
			], $request, 200, 'Đồng bộ phương thức thu hồi');
		} catch (\Throwable $th) {
			DB::rollBack();
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
