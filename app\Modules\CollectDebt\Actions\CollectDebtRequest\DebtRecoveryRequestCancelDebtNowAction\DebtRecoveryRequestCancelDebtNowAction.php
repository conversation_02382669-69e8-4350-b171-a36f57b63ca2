<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCancelDebtNowAction;

use Exception;
use App\Lib\Helper;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\ParameterBag;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtPartner\DebtRecoveryPartnerCreateRequest;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCreateAction\SubAction\XuLyPartnerTrichNgayMposSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\CheckRequestViaMposSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCancelPaymentAction\DebtRecoveryRequestCancelPaymentAction;

class DebtRecoveryRequestCancelDebtNowAction
{
	public function run(Request $request)
	{
		$collectDebtRequest = CollectDebtRequest::query()->findOrFail($request->json('data.request_id'));

		throw_if(!$collectDebtRequest, new Exception('Lệnh trích không tồn tại'));

		throw_if(
			!$collectDebtRequest->isLenhTrichNgayDaHachToanNhungChuaBiHuyVaChuaCoKetQuaTrich(),
			new Exception('Lệnh trích ngay có trạng thái chưa phù hợp để hủy')
		);

		/**
		 * 1. Gọi check sang mpos
		 * 		+ nếu mpos trả về là PENDING thì gọi tiếp api hủy
		 * 		+ nếu mpos trả về là  KHÁC PENDING thì ngưng
		 * 2. Gọi api hủy lệnh
		 * 		+ hủy thành công -> tạo yêu cầu điều chỉnh
		 * 		+ hủy thất bại -> kệ nó
		 */
		$checkResultMpos = app(CheckRequestViaMposSubAction::class)->run(
			$collectDebtRequest,
			$request,
			false
		);

		if ( !empty($checkResultMpos['data']['status']) ) {
			if ($checkResultMpos['data']['status'] == 'CANCEL') {
				return $this->taoDieuChinh($collectDebtRequest, $request);
			}

			if ($checkResultMpos['data']['status'] == 'APPROVED') {
				throw new Exception('Lệnh trích đã được MPOS duyệt, không thể hủy');
			}

			if ($checkResultMpos['data']['status'] == 'SUCCESS') {
				throw new Exception('Lệnh trích đã được MPOS duyệt, không thể hủy');
			}

			$cancelMposRequest = app(DebtRecoveryRequestCancelPaymentAction::class)->run($collectDebtRequest);
          
			throw_if(
				$cancelMposRequest['data']['error_code'] != '00',
				new Exception('Lỗi không thể hủy lệnh: ' . json_encode($cancelMposRequest))
			);

			return $this->taoDieuChinh($collectDebtRequest, $request);
		}

		throw new Exception('Bạn không thể hủy yêu cầu rút tiền nhanh do tối tác đã có thông tin trích nợ');
	}

	public function taoDieuChinh($collectDebtRequest, $request) {
		// Đoạn này là cancel thành công, tạo yêu cầu điều chỉnh 0đ
		$collectDebtPartner = CollectDebtPartner::query()
		->where('payment_method_code', 'MPOS')
		->where('partner_request_id', $collectDebtRequest->partner_request_id)
		->first();


		$debtRecoveryPartnerCreateRequest = new DebtRecoveryPartnerCreateRequest();
		$debtRecoveryPartnerCreateRequest->setJson( new ParameterBag($this->__buildRequestInput($request)) );

		$createAdjustmentResultBoolean = app(XuLyPartnerTrichNgayMposSubAction::class)->run(
		$collectDebtPartner,
		$collectDebtRequest,
		$debtRecoveryPartnerCreateRequest
		);

		throw_if(!$createAdjustmentResultBoolean, new Exception('Không tạo được yêu cầu điều chỉnh'));

		CollectDebtRequest::query()
		->where('id', $collectDebtRequest->id)
		->update([
		'time_canceled' => time(),
		'time_canceled_payment' => time(),
		'canceled_by' => $request->json('data.users_admin_id'),
		'canceled_payment_by' => $request->json('data.users_admin_id'),
		]);

		return $createAdjustmentResultBoolean;
	}

	private function __buildRequestInput(Request $request): array {
		$userAction = $request->json('data.users_admin_id', Helper::getCronJobUser());
		$userAction = json_decode($userAction, true);

		return [
			'data' => [
				'amount_receiver' => 0, // do là hủy thì coi như thu 0đ,
				'user_action' => [
					'id' => $userAction['id'] ?? 'cronjob',
					'username' => $userAction['username'] ?? 'cronjob',
					'mobile' => $userAction['mobile'] ?? '',
				]
			]
		];
	}
}  // End class