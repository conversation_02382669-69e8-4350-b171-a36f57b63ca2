<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestRecheckAction;

use App\Lib\Helper;
use App\Modules\CollectDebt\Model\CollectDebtLog;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Symfony\Component\HttpFoundation\ParameterBag;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtPartner\DebtRecoveryPartnerCreateRequest;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCreateAction\SubAction\XuLyPartnerTrichNgayMposSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCancelPaymentAction\DebtRecoveryRequestCancelPaymentAction;

class XuLyRecheckChoLenhTrichNgaySubAction
{
	public function updateLogVeChuaXuLy($collectDebtRequest) 
	{
		$collectDebtLog = CollectDebtLog::query()
																		->where('partner_transaction_id', $collectDebtRequest->partner_transaction_id)
																		->where('service_code', 'CHECK_DEBT_NOW')
																		->first();
																		
		if ($collectDebtLog) {
			$collectDebtLog->status = CollectDebtEnum::RL_STT_MOI_TAO;
			$collectDebtLog->save();
		}

		return $collectDebtRequest;
	}

	/***
   * array:6 [▼
      "api_request_id" => null
      "success" => true
      "checksum" => "ac54fa01105d54298f52fa9cfa080d8d"
      "result_code" => 200
      "message" => "Request success"
      "data" => array:5 [▼
        "error_code" => "00"
        "description" => "SUCCESS"
        "status" => "SUCCESS"
        "amount_debit" => "0"
        "partner_transaction_id" => "38931752"
      ]
    ]
   *
   * @param CollectDebtRequest $collectDebtRequest [explicite description]
   * @param Request $request [explicite description]
   *
   * @return void
   */
	public function run($checkMposResult, CollectDebtRequest $collectDebtRequest)
	{
		if (!empty($checkMposResult['data']['error_code']) && $checkMposResult['data']['error_code'] == '1004') {
			$this->updateLogVeChuaXuLy($collectDebtRequest);
			return $collectDebtRequest;
		}

		switch (@$checkMposResult['data']['status']) {
			// Thành công -> cuối cùng
			case 'SUCCESS':
			case 'APPROVED': 
			case 'APPROVE': 
				$this->xuLyTrangThaiThanhCongCuoiCung($collectDebtRequest, $checkMposResult);
				break;

			case 'CANCEL':
				$this->xuLyTrangThaiThanhCongCuoiCung($collectDebtRequest, $checkMposResult, true);
				break;

			case 'TIMEOUT':
				$this->updateLogVeChuaXuLy($collectDebtRequest);
				return $collectDebtRequest;

				break;

			// Ngược lại phải hủy cho bằng thành công thì thôi
			default:
				$isDaHuyLenh = false;
					
				while(!$isDaHuyLenh) {
					$cancelMposRequest = app(DebtRecoveryRequestCancelPaymentAction::class)->run($collectDebtRequest);
					
					$isDaHuyLenh = ( 
						isset($cancelMposRequest['data']['error_code']) && $cancelMposRequest['data']['error_code'] == '00'
					);

					if ($isDaHuyLenh) {
						$this->xuLyTrangThaiThanhCongCuoiCung($collectDebtRequest, $checkMposResult, true);
					}
				}
				break;
		}

		$collectDebtRequest->refresh();

		$collectDebtRequest->other_data = $collectDebtRequest->putOtherData([
			'type' => 'RECHECK_DEBT_NOW_REQUEST',
			'time_modified' => time(),
			'note' => sprintf('[ %s ] - Kiểm tra lại lệnh TRÍCH NGAY đã hết hạn', $checkMposResult['data']['status']),
			'data' => $checkMposResult
		]);
		
		$collectDebtRequest->save();
		return $collectDebtRequest;
	} // End method


	public function xuLyTrangThaiThanhCongCuoiCung(CollectDebtRequest $collectDebtRequest, array $checkMposResult, bool $isHuyThanhCong=false) {
		if ($isHuyThanhCong) {
			$soTienTrichThanhCong = 0;
		}

		if (!$isHuyThanhCong) {
			$mposStatus = $checkMposResult['data']['status'];
			$soTienTrichThanhCong = 0;

			if ($mposStatus == 'SUCCESS') {
				$soTienTrichThanhCong = !empty($checkMposResult['data']['amount_debit']) ? $checkMposResult['data']['amount_debit'] : 0;
			}

			if ($mposStatus == 'APPROVE' || $mposStatus == 'APPROVED') {
				$soTienTrichThanhCong = !empty($checkMposResult['data']['amount_debit']) ? $checkMposResult['data']['amount_debit'] : 0;
			}

			if ($mposStatus == 'EXPIRED') {
				$soTienTrichThanhCong = 0;
			}

			if ($mposStatus == 'CANCEL') {
				$soTienTrichThanhCong = 0;
			}

			if ($mposStatus == 'PENDING') {
				$soTienTrichThanhCong = 0;
			}
		}
    

		// MPOS trích đủ tiền
    if ($soTienTrichThanhCong == $collectDebtRequest->amount_receiver) {
			$collectDebtRequest->forceFill([
				'time_completed_recheck' => time(),
				'time_receivered' => time(),
				'completed_recheck_by' => Helper::getCronJobUser(),
				'receivered_by' => Helper::getCronJobUser()
			]);
		}

		// MPOS trích thiếu tiền, đoạn này cần đi vào luồng điều chỉnh
		if ($soTienTrichThanhCong < $collectDebtRequest->amount_receiver) {
			$collectDebtPartner = CollectDebtPartner::query()->where('partner_request_id', $collectDebtRequest->partner_request_id)->first();
			
			$params = [
				'data' => [
					'payment_channel_code' => $collectDebtPartner->payment_channel_code,
					'payment_method_code' => $collectDebtPartner->payment_method_code,
					'payment_account_id' =>  $collectDebtPartner->payment_account_id,
					'partner_request_id' =>  $collectDebtPartner->partner_request_id,
					'partner_transaction_id' => $collectDebtPartner->partner_transaction_id,
					'amount_receiver' => $soTienTrichThanhCong,
					'fee' => $collectDebtPartner->fee,
					'request_exists' => CollectDebtEnum::PARTNER_REQUEST_DA_CO_YC_THANH_TOAN,
					'response' => '{}',
					'description' => 'Recheck yeu cau trich ngay',
					'created_by' => Helper::getCronJobUser()
				]
			];

			$rq = new DebtRecoveryPartnerCreateRequest();
			$rq->setJson(new ParameterBag($params));

			$yeuCauDieuChinh = app(XuLyPartnerTrichNgayMposSubAction::class)->run(
				$collectDebtPartner,
				$collectDebtRequest,
				$rq
			);
		}

		return $collectDebtRequest;
  }
} // End class
