<?php
namespace App\Modules\CollectDebt\routes;

use Illuminate\Support\Facades\Route; 
use App\Modules\CollectDebt\Controllers\v1\CollectDebtDigitalNoti\CollectDebtDigitalNotiController;


/**
 * Build param: chạy 1 phút/lần
 */
Route::any('/DigitalNotiBuildParam', [
	'as' => 'BuildParamDigitalNotiAction',
	'uses' => CollectDebtDigitalNotiController::class . '@DigitalNotiBuildParam'
]);

/**
 * Gửi noti: chạy 1 phút/lần
 */
Route::any('/DigitalNotiSend', function () {
	return ['send' => 'ok'];
});

/**
 * Sắp đến hạn gọi (các mốc trước hạn tất toán 3-2-1 ngày)
 * Chạy 01 lần duy nhất vào 10h sáng hàng ngày
 */
Route::any('/DigitalNotiSapDenHan', [
	'as' => 'DigitalNotiSapDenHanAction',
	'uses' => CollectDebtDigitalNotiController::class . '@DigitalNotiSapDenHan'
]);

/**
 * Sắp đến kỳ thanh toán (trước mốc ngày chu kỳ trích T: các môc 3-2-1 ngày)
 * Chạy 01 lần duy nhất vào 10h sáng hàng hàng
 */
Route::any('/DigitalNotiSapDenKyThanhToan', [
	'as' => 'DigitalNotiSapDenKyThanhToanAction',
	'uses' => CollectDebtDigitalNotiController::class . '@DigitalNotiSapDenKyThanhToan'
]);

/**
 * Nhắc đến hạn hợp đồng
 * Gọi 01 lần duy nhất vào 10h sáng hàng ngày
 */
Route::any('/DigitalNotiDenHanHopDong', [
	'as' => 'DigitalNotiDenHanHopDongAction',
	'uses' => CollectDebtDigitalNotiController::class . '@DigitalNotiDenHanHopDong'
]);

/**
 * Xóa dữ liệu noti không cần thiết
 * Chạy 1 lần duy nhất lúc 22h
 */
Route::any('/DigitalNotiClearData', [
	'as' => 'DigitalNotiClearDataAction',
	'uses' => CollectDebtDigitalNotiController::class . '@DigitalNotiClearData'
]);

/**
 * HĐ phát sinh quá hạn
 * Chạy job 01 lần duy nhất vào lúc 11h hàng ngày
 */
Route::any('/DigitalNotiQuaHan', [
	'as' => 'DigitalNotiQuaHanAction',
	'uses' => CollectDebtDigitalNotiController::class . '@DigitalNotiQuaHan'
]);


