<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction;

use Exception;
use Throwable;
use App\Lib\ApiCall;
use App\Utils\CommonVar;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Model\CollectDebtPlan;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction\Task\XuLyYeuCauLoiRoRangTask;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction\Task\XuLyYeuCauGuiLaiQuaMposTask;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction\Task\XuLyYeuCauLoiKhongRoRangTask;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction\Task\XuLyYeuCauGuiThanhCongQuaMposTask;

class SendRequestViaMposSubAction
{
  /**
   * Gửi lệnh sang MPOS
   *
   * @param CollectDebtRequest $currentCollectDebtRequest [Yêu cầu hiện tại qua cần phải gửi qua kênh MPOS]
   *
   * @return CollectDebtRequest
   */
  public function run(CollectDebtRequest $currentCollectDebtRequest): CollectDebtRequest
  {
    $collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $currentCollectDebtRequest->contract_code)->first();

		$firstPlan = CollectDebtPlan::query()->where('contract_code', $currentCollectDebtRequest->contract_code)
																				 ->orderBy('id', 'ASC')
																				 ->select(['amount_period_debit', 'debit_begin'])
																				 ->first();

    try {
      $params = [
        'partner_request_id' => $currentCollectDebtRequest->partner_request_id, // mã giao dịch gửi sang đối tác, do NL sinh ra
        'partner_merchant_id' => $currentCollectDebtRequest->getPaymentAccountId(), // MC id, mã MC này là của hệ thống MPOS
        'amount_payment' => $currentCollectDebtRequest->amount_request, // số tiền yc trích
        'contract_code' => $currentCollectDebtRequest->contract_code, // mã HĐ
        'loan_original_amount' => $collectDebtSummary->getSoTienGiaiNgan(), // Số tiền giải ngân: Fix tạm bằng 0, SAU NÀY PHẢI SỬA LẠI, LẤY TỪ CHỈ DẪN
        'deduction_per_day_amount' => $firstPlan->amount_period_debit, // Số tiền trích nợ hằng ngày: Là số tiền chia đều cho các kỳ
        'loan_balance' => $firstPlan->debit_begin, // Dư nợ đầu kỳ = Lấy debit_begin
        'request_id' => $currentCollectDebtRequest->id, // ID yêu cầu của NL
        'users_admin_id' => request()->json('data.users_admin_id', 'cronjob'), // Người thao tác
        'time_begin' => $currentCollectDebtRequest->time_begin, // Thời gian bắt đầu lệnh gửi
        'time_end' => $currentCollectDebtRequest->time_expired, // Thời gian kết thúc lệnh gửi
        'payment_channel' => 'MPOS' // kênh thu
      ];

      $payload = [
        'module' => CommonVar::API_REQUEST_DEBT_MODULE,
        'path' => '/partner-collect-debt-gateway/send-debt',
        'params' => $params,
        'method' => 'POST'
      ];

      $sendDebtResult = (new ApiCall())->callFunctionApi($payload, true);
			
      // Lỗi không rõ ràng
      if (empty($sendDebtResult) || empty($sendDebtResult['data']['error_code'])) {
        return app(XuLyYeuCauLoiKhongRoRangTask::class)->run($currentCollectDebtRequest, $sendDebtResult);
      }

      if (!empty($sendDebtResult['data']['error_code'])) {
        switch ($sendDebtResult['data']['error_code']) {
          // thành công
          case '00':
            return app(XuLyYeuCauGuiThanhCongQuaMposTask::class)->run($currentCollectDebtRequest, $sendDebtResult);

          // lỗi trùng
          case '-46001':
            return app(XuLyYeuCauGuiLaiQuaMposTask::class)->run($currentCollectDebtRequest);

          // lỗi rõ ràng
          default:
            return app(XuLyYeuCauLoiRoRangTask::class)->run($currentCollectDebtRequest, $sendDebtResult);
        }
      }
    } catch (Throwable $th) {
			$message = sprintf('env: %s --- err: %s', config('app.env'), $th->getMessage());
      TelegramAlert::sendRqToPartner($message);
      throw new Exception($message);
    }

    return $currentCollectDebtRequest;
  } // End method
} // End class