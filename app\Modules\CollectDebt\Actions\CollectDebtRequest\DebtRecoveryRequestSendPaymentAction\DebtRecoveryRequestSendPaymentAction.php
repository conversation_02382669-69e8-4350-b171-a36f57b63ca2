<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use App\Modules\CollectDebt\Enums\CacheEnum;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction\SendRequestViaMposSubAction;

class DebtRecoveryRequestSendPaymentAction
{
	private array $__returnData = [];

	private array $__excludeData = [];

	public function init() {
		for ($i = 1; $i <= 30; $i++) {
			DB::beginTransaction();
			try {
				$listRequest = $this->run();
				if (!$listRequest || $listRequest->isEmpty()) {
					$this->__returnData[] = "EMPTY";
					DB::commit();
					break;
				}

				$this->__returnData[] = $listRequest->first()->partner_request_id;
				DB::commit();
			}catch(\Throwable $th) {
				DB::rollBack();
			} finally {
				usleep(100000);
			}
		}

		return $this->__returnData;
	}

	/**
	 * Logic truy vấn của job
	 * Lấy ra danh sách các bản ghi yêu cầu đạt các yếu tố sau
	 *  	> Yêu cầu PHẢI GỬI sang kênh đối tác
	 *  	> Yêu cầu có trạng thái thanh toán (status_payment) là 1 (Chưa gửi)
	 *  	> Yêu cầu có trạng thái ghi sổ (status_recored) là 1 (Chưa ghi sổ)
	 * 		> Yêu cầu phải đang còn hạn (chưa bị expired)
	 *
	 * @return void
	 */
	public function run()
	{
		$collectDebtRequests = CollectDebtRequest::query()
			->with('collectDebtShareOnly:contract_code,priority,partner_code')
			->where('payment_method_code', 'MPOS')
			->where('status_recored', CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO)
			->where('is_payment', CollectDebtEnum::REQUEST_IS_PAYMENT_CO_GUI_DOI_TAC_TT)
			->where('status_payment', CollectDebtEnum::REQUEST_STT_PM_CHUA_GUI)
			->where('create_from', CollectDebtEnum::REQUEST_LOAI_TRICH_TU_DONG)
			->where(function ($q) {
				$q->whereNull('time_sended')
					->orWhereRaw(sprintf('time_sended + %d < %d', env('THOI_GIAN_XU_LY_LENH_LOI_RO_RANG', 20) * 60, time()));
			})
			->where('time_expired', '>', time());

		if (!empty($this->__excludeData)) {
			$collectDebtRequests = $collectDebtRequests->whereNotIn('id', $this->__excludeData);
		}

		$collectDebtRequests = $collectDebtRequests->lock(" FOR UPDATE SKIP LOCKED ")
			->limit(1)
			->get();

		if ($collectDebtRequests->isEmpty()) {
			return $collectDebtRequests;
		}

		$now = now();

		foreach ($collectDebtRequests as $collectDebtRequest) {

			$this->__excludeData[] = $collectDebtRequest->id;

			if ($collectDebtRequest->isUnsentPayment()) {

				if ($collectDebtRequest->collectDebtShareOnly->priority == 1 && $now->lt(today()->addMinutes(30))) {
					continue;
				}

				$this->__excludeData[] = $collectDebtRequest->id;

				$rq = app(SendRequestViaMposSubAction::class)->run($collectDebtRequest);

			}
		}

		return $collectDebtRequests;
	}
} // End class