<?php

namespace App\Modules\CollectDebt\Model;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\Traits\CollectDebtRequest\RequestPlanData;
use App\Modules\CollectDebt\Model\Traits\CollectDebtRequest\RequestLedgerable;
use App\Modules\CollectDebt\Model\Traits\CollectDebtRequest\RequestStatusable;
use App\Modules\CollectDebt\Model\Traits\CollectDebtRequest\RequestExpiredable;
use App\Modules\CollectDebt\Model\Traits\CollectDebtRequest\TimelineMethodTrait;
use App\Modules\CollectDebt\Model\Traits\CollectDebtRequest\UpdateOtherDataTrait;

class CollectDebtRequest extends Model
{
	use TimelineMethodTrait,
			UpdateOtherDataTrait, 
			RequestPlanData,
			RequestStatusable,
      RequestExpiredable,
			RequestLedgerable;

	protected $table 	 = 'debt_recovery_request';
	public $timestamps = false;
	protected $appends = [
		'time_begin_as_date',
		'time_expired_as_date',
		'time_begin_as_vn_date',
		'time_expired_as_vn_date',
		'timeline'
	];

	protected $guarded = [];


	/* -------------------- Relationship ----------------- */
	public function collectDebtPartner()
	{
		return $this->belongsTo(CollectDebtPartner::class, 'partner_transaction_id', 'partner_transaction_id');
	}

	public function collectDebtPartnerRequestId()
	{
		return $this->belongsTo(CollectDebtPartner::class, 'partner_request_id', 'partner_request_id');
	}

	public function collectDebtLedger()
	{
		return $this->hasOne(CollectDebtLedger::class, 'request_id', 'id');
	}

	public function ledgers()
	{
		return $this->hasMany(CollectDebtLedger::class, 'request_id', 'id');
	}

	public function collectDebtRequestActions() {
		return $this->hasMany(CollectDebtRequestAction::class, 'request_id', 'id');
	}

	public function collectDebtShare() {
		return $this->belongsTo(CollectDebtShare::class, 'contract_code', 'contract_code');
	}

	public function collectDebtShareOnly() {
		return $this->belongsTo(CollectDebtShareOnly::class, 'contract_code', 'contract_code');
	}
/* -------------------- Appends ----------------- */
	public function getTimeBeginAsDateAttribute()
	{
		return Carbon::createFromTimestamp($this->time_begin);
	}

	public function getTimeBeginAsVnDateAttribute()
	{
		return Carbon::createFromTimestamp($this->time_begin)->format('d/m/Y H:i:s');
	}

	public function getTimeExpiredAsDateAttribute()
	{
		return Carbon::createFromTimestamp($this->time_expired);
	}

	public function getTimeExpiredAsVnDateAttribute()
	{
		return Carbon::createFromTimestamp($this->time_expired)->format('d/m/Y H:i:s');
	}

	public function getTimeCompletedAttribute()
	{
		if (!empty($this->time_completed)) {
			return $this->time_completed;
		}

		return $this->time_completed_recheck ?? 0;
	}

	public function getStatusAttribute($value)
	{
		if ( $this->create_from == CollectDebtEnum::REQUEST_LOAI_TRICH_TU_DONG 
			   || $this->isDaDuyetGiamPhiBuoc2()
				 || $value == CollectDebtEnum::REQUEST_STT_TU_CHOI
				 || $value == CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH
		) {
			return $value;
		}

		if ($this->create_from == CollectDebtEnum::REQUEST_LOAI_TRICH_TAY) {
			if ($this->isHinhThucTrich() == 'GIAM_PHI') {
				if (!empty($this->isThoigianDuyet2())) {
					return CollectDebtEnum::REQUEST_STT_DA_DUYET_GIAM_PHI_CAP_2;
				}

				if (!empty($this->time_approved)) {
					return CollectDebtEnum::REQUEST_STT_DA_DUYET_GIAM_PHI_CAP_1;
				}
			}
		}

		return $value;
	}
	/* -------------------- Methods ----------------- */
	public function isAutoDebt(): bool
	{
		return $this->create_from == 1 && $this->payment_channel_code == 'MPOS';
	}

	public function isManualDebt(): bool
	{
		return $this->create_from == 2;
	}

	public function isTrichTayGiamPhi(): bool
	{
		return $this->isManualDebt() && $this->isHinhThucTrich() == 'GIAM_PHI';
	}

	public function isTrichTayGiamPhiVaChuaDuyetBuoc1(): bool
	{
		return $this->isTrichTayGiamPhi() && empty($this->time_approved) && $this->isChuaHuyTrichTay();
	}

	public function isTrichTayGiamPhiVaChuaDuyetBuoc2(): bool
	{
		return $this->isTrichTayGiamPhi() && !empty($this->time_approved) && empty($this->isThoigianDuyet2()) && $this->isChuaHuyTrichTay();
	}

	public function isDaDuyetGiamPhiBuoc2(): bool
	{
		return $this->isTrichTayGiamPhi() && !empty($this->isThoigianDuyet2());
	}

	public function isTrichTayThuGoc(): bool
	{
		return $this->isManualDebt() && !empty($this->isHinhThucTrich()) && $this->isHinhThucTrich() != 'GIAM_PHI';
	}

	public function isChuaHuyTrichTay(): bool
	{
		return $this->isManualDebt() && empty($this->time_canceled);
	}

	public function isDaHuyTrichTay(): bool
	{
		return $this->isManualDebt() && !empty($this->time_canceled);
	}

	public function isTrichTayThuGocVaChuaDuyet(): bool
	{
		return $this->isTrichTayThuGoc() && empty($this->time_approved) && $this->isChuaHuyTrichTay();
	}

	
	public function getPlanIds(): array
	{
		if (empty($this->plan_ids)) {
			return [];
		}

		return explode(',', $this->plan_ids);
	}


	public function isPaymentViaChannelMpos(): bool
	{
		return strtoupper($this->payment_channel_code) == 'MPOS';
	}

	public function isYeuCauPhaiGuiQuaKenhDoiTac(): bool
	{
		return $this->is_payment == CollectDebtEnum::REQUEST_IS_PAYMENT_CO_GUI_DOI_TAC_TT;
	}

	public function putOtherData($data = []): string
	{
		$otherData = json_decode($this->other_data, true);
		$otherData[] = $data;
		return json_encode($otherData, JSON_UNESCAPED_UNICODE);
	}

	public function getOtherData(): array
	{
		return json_decode($this->other_data, true);
	}

	public function getRequestOtherData(): array
	{
		return json_decode($this->other_data, true);
	}


	// Load ra toàn bộ lịch thu của các yêu cầu (đầu vào)
	public static function loadSchedules(Collection $collectDebtRequests): Collection
	{
		$planIds = [];
		foreach ($collectDebtRequests as $collectDebtRequest) {
			$planIds = array_merge($planIds, $collectDebtRequest->getPlanIds());
		}

		$schedules = CollectDebtSchedule::whereIn('id', $planIds)->orderByRaw('time_start ASC, id ASC')->get();
		return $schedules;
	}

	// Lấy ra danh sách lịch của 1 yêu cầu đó
	public function getCollectDebtSchedules(): Collection
	{
		$planIds = $this->getPlanIds();
		$collectDebtSchedules = CollectDebtSchedule::whereIn('id', $planIds)->orderBy('time_start', 'ASC')->get();
		return $collectDebtSchedules;
	}



	// Lấy ra ID MC trên kênh đối tác
	public function getPaymentAccountId(): string
	{
		return $this->payment_account_id;
	}
	/************* status **************/
	public function isNew(): bool
	{
		return $this->status == CollectDebtEnum::REQUEST_STT_MOI_TAO;
	}

	public function isApproved(): bool
	{
		return $this->status == CollectDebtEnum::REQUEST_STT_DA_DUYET;
	}

	public function isCompleted(): bool
	{
		return $this->status == CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH;
	}

	public function isCanceled(): bool
	{
		return $this->status == CollectDebtEnum::REQUEST_STT_TU_CHOI;
	}

	public function isDaTuChoiBangTay(): bool {
		return !empty($this->time_canceled);
	}

	public function isNeedCompleteAndReCheck(): bool
	{
		return $this->status == CollectDebtEnum::REQUEST_STT_CAN_HOAN_THANH_VA_KIEM_TRA;
	}

	public function isRequestFinalStatus(): bool {
		return $this->isCompleted() || $this->isCanceled() || $this->isNeedCompleteAndReCheck();
	}

	public function canMarkRequestAsCompleted(): bool
	{
		return $this->isApproved() && $this->status_payment == CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA
			&& $this->status_recored == CollectDebtEnum::REQUEST_STT_RC_DA_GHI_SO;
	}

	public static function listingStatus(): array
	{
		return [
			CollectDebtEnum::REQUEST_STT_MOI_TAO => 'Mới tạo',
			CollectDebtEnum::REQUEST_STT_DA_DUYET => 'Đã duyệt',
			CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH => 'Đã hoàn thành',
			CollectDebtEnum::REQUEST_STT_TU_CHOI => 'Từ chối',
			CollectDebtEnum::REQUEST_STT_CAN_HOAN_THANH_VA_KIEM_TRA => 'Cần hoàn thành và kiểm tra lại'
		];
	}

	/************* status_payment **************/
	public function isUnsentPayment(): bool
	{
		return $this->status_payment == CollectDebtEnum::REQUEST_STT_PM_CHUA_GUI;
	}

	public function isSendingPayment(): bool
	{
		return $this->status_payment == CollectDebtEnum::REQUEST_STT_PM_DANG_GUI;
	}

	public function isCancelPayment(): bool
	{
		return $this->status_payment == CollectDebtEnum::REQUEST_STT_PM_TU_CHOI;
	}

	public function isSentErrorPayment(): bool
	{
		return $this->status_payment == CollectDebtEnum::REQUEST_STT_PM_GUI_LOI;
	}

	public function isSentPayment(): bool
	{
		return $this->status_payment == CollectDebtEnum::REQUEST_STT_PM_DA_GUI;
	}

	public function isReceivedPaymentResult(): bool
	{
		return $this->status_payment == CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA;
	}

	public static function listingStatusPayment(): array
	{
		return [
			CollectDebtEnum::REQUEST_STT_PM_CHUA_GUI => 'Chưa gửi',
			CollectDebtEnum::REQUEST_STT_PM_DANG_GUI => 'Đang gửi',
			CollectDebtEnum::REQUEST_STT_PM_TU_CHOI => 'Từ chối',
			CollectDebtEnum::REQUEST_STT_PM_GUI_LOI => 'Gửi lỗi',
			CollectDebtEnum::REQUEST_STT_PM_DA_GUI => 'Đã gửi',
			CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA => 'Đã nhận kết quả'
		];
	}

	public function isFinalPaymentStatus(): bool
	{
		return $this->isReceivedPaymentResult() || $this->isCancelPayment();
	}
	/************* status_recored **************/
	public function isUnRecorded(): bool
	{
		return $this->status_recored == CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO;
	}

	public function isRecording(): bool
	{
		return $this->status_recored == CollectDebtEnum::REQUEST_STT_RC_DANG_GHI_SO;
	}

	public function isRecorded(): bool
	{
		return $this->status_recored == CollectDebtEnum::REQUEST_STT_RC_DA_GHI_SO;
	}

	public function isRequestDaGhiSo(): bool
	{
		return $this->status_recored == CollectDebtEnum::REQUEST_STT_RC_DA_GHI_SO;
	}

	public function isRecordedError(): bool
	{
		return $this->status_recored == CollectDebtEnum::REQUEST_STT_RC_GHI_SO_LOI;
	}

	public static function listingStatusRecorded(): array
	{
		return [
			CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO => 'Chưa ghi sổ',
			CollectDebtEnum::REQUEST_STT_RC_DANG_GHI_SO => 'Đang ghi sổ',
			CollectDebtEnum::REQUEST_STT_RC_DA_GHI_SO => 'Đã ghi sổ',
			CollectDebtEnum::REQUEST_STT_RC_GHI_SO_LOI => 'Ghi sổ lỗi',
		];
	}


	/************* other_data **************/
	public function isHinhThucTrich()
	{
		$otherData = json_decode($this->other_data, true);

		return collect($otherData)->where('type', 'OTHER')->where('data.manual_request_type', '!=', '')
			->map(function ($item) {
				return isset($item['data']['manual_request_type']) ? $item['data']['manual_request_type'] : '';
			})->first();
	}

	public function isNguoiDuyet2()
	{
		$otherData = json_decode($this->other_data, true);

		return collect($otherData)->where('type', 'OTHER')->where('data.approved_step2_by', '!=', '')
			->map(function ($item) {
				return isset($item['data']['approved_step2_by']) ? $item['data']['approved_step2_by'] : '';
			})->first();
	}

	public function isThoigianDuyet2()
	{
		$otherData = json_decode($this->other_data, true);

		return collect($otherData)->where('type', 'OTHER')->where('data.time_approved_step2', '!=', '')
			->map(function ($item) {
				return isset($item['data']['time_approved_step2']) ? $item['data']['time_approved_step2'] : '';
			})->first();
	}

	public function isTrichNgay()
	{
		$otherData = json_decode($this->other_data, true);

		return collect($otherData)->where('type', 'OTHER')->where('data.is_debt_now', '!=', '')
			->map(function ($item) {
				return isset($item['data']['is_debt_now']) ? $item['data']['is_debt_now'] : '';
			})->first();
	}

  public function isCoTheHuyYeuCauSangDoiTac(): bool {
    // chưa ghi sổ và empty partner hoặc partner == 0;
    return $this->isUnRecorded() && (empty($this->collectDebtPartner) || !$this->collectDebtPartner->canPaidCollectRequest());
  }

	public function isYeuCauCutOff(): bool {
		$otherData = $this->getOtherData();
		
		$isDanhDauCutOff = collect($otherData)->contains(function ($item) {
			return $item['type'] == 'CUTOFF';
		});

		return $isDanhDauCutOff || $this->status_cutoff == CollectDebtEnum::REQUEST_DA_XL_CUT_OFF;
	}

	public function isThoiDiemHachToanYeuCauTrungVoiHienTai(): bool {
		$timeExpired = Carbon::createFromTimestamp($this->time_expired);
		return now()->isSameDay($timeExpired);
	}

	public function isYeuCauDaGiamTruTienTrongVi(): bool {
		$requestOtherData = $this->getRequestOtherData();
		return collect($requestOtherData)->contains(function ($item) {
			return $item['type'] == 'OPEN_AND_MINUS_FREEZE';
		});
	}
} // End class
