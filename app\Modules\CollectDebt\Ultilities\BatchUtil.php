<?php

namespace App\Modules\CollectDebt\Ultilities;


class BatchUtil
{
	public static function getRanges(int $min, int $max, int $batchCount, int $batchSize)
	{
		$ranges = [];
		$count = 0;
		$current = $min;

		while ($count < $batchCount && $current <= $max) {
			$end = $current + $batchSize - 1;
			if ($end > $max) {
				$end = $max;
			}

			$ranges[] = [$current, $end];

			$current = $end + 1; // dịch sang record kế tiếp
			$count++;
		}

		return $ranges;
	}
} // End class
