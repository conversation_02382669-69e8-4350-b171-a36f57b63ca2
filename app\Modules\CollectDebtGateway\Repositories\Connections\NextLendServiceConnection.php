<?php

namespace App\Modules\CollectDebtGateway\Repositories\Connections;

use App\Lib\Encryption;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Log;

class NextLendServiceConnection
{
  public $_API_URL            = '';
  public $_API_APP_SECRET_KEY = '';
  public $_API_KET_ENCRYPTION_DATA = '';
  public $_version = '';
  public $_channelCode = '';

  public $_log;
  public $uniqueKeyLog = '';

  public $timeStart;
  public $apiDuring;
  public $errorCode;

  private $__coreResponseResult;

  public function __construct()
  {
    $this->timeStart = now();
    $this->apiDuring = microtime(true);

    $this->_API_URL = config('nextlend.NEXTLEND_SERVICE_REQUEST_URL');
    $this->_API_APP_SECRET_KEY = config('nextlend.NEXTLEND_SERVICE_SECRET_KEY');
    $this->_API_KET_ENCRYPTION_DATA = config('nextlend.NEXTLEND_SERVICE_ENCRYPT_KEY');
    $this->_version = config('nextlend.NEXTLEND_SERVICE_VERSION');
    $this->_channelCode = config('nextlend.NEXTLEND_SERVICE_CHANNEL_CODE', 'NEXTLENDV4');
  }


  public function callRequest(array $inputs = [], string $functionName = '', string $method = 'POST')
  {
		$logContext = [
			'func' => $functionName,
			'inputRaw' => $inputs,
		];

    $requestInput = $this->_makeRequestInput($inputs, $functionName);

    $dataPost = json_encode($requestInput, JSON_UNESCAPED_UNICODE);
    $logContext['requestInput'] = $requestInput;

    $headerArray = array(
      "cache-control: no-cache",
      "content-type: application/json; charset=UTF-8",
    );

    $curl = curl_init();

    curl_setopt_array($curl, array(
      CURLOPT_URL => $this->_API_URL,
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_HTTPAUTH => CURLAUTH_ANY,
      CURLOPT_SSL_VERIFYPEER => false,
      CURLOPT_FOLLOWLOCATION => true,
      CURLOPT_CUSTOMREQUEST => $method,
      CURLOPT_POSTFIELDS => $dataPost,
			CURLOPT_CONNECTTIMEOUT => config('nextlend.NL_PARTNER_CONNECTION_TIMEOUT', 3),
      CURLOPT_TIMEOUT => config('nextlend.NL_PARTNER_TIMEOUT', 5),
      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
      CURLOPT_HTTPHEADER => $headerArray
    ));

    $response = curl_exec($curl);
    $resultStatus = curl_getinfo($curl);

    curl_close($curl);

		$lendingRequestId = $inputs['lendingRequestId'] ?? '';

    if ($resultStatus['http_code'] == 200) {
      $this->__coreResponseResult = json_decode($response, true);
			$logContext['Result'] = $this->__coreResponseResult;
     
      if (gettype($this->__coreResponseResult['data']) == 'string') {
        $decode = json_decode($this->__coreResponseResult['data'], true);
				$logContext['ParseError'] = $decode;
      }

      Log::info("[$functionName] ---->$lendingRequestId", $logContext);
      $this->errorCode = isset($this->__coreResponseResult['RespCode']) ? $this->__coreResponseResult['RespCode'] : '';
      return $this->__coreResponseResult;
    }else {
			$logContext['error'] = "HTTP CODE: " . json_encode($response);
		}

   	Log::info("[NextlendService][$functionName] ---->$lendingRequestId", $logContext);
    return false;
  }

  public function _makeRequestInput(array $params = [], string $func = ''): array
  {
    $hash          = $this->_buildHash($params);
    $chuoiTruocMd5 = $func . $this->_version . $this->_channelCode . $hash . $this->_API_APP_SECRET_KEY;
    $checkSum      = md5($chuoiTruocMd5);

    return [
      'Fnc'             => $func,
      'Checksum'         => $checkSum,
      'EncData'           => $hash,
      'ChannelCode' => $this->_channelCode,
      'Version' => $this->_version,
    ];
  }

  protected function _buildHash(array $params = []): string
  {
    $paramsJsonEncode = json_encode($params, JSON_UNESCAPED_UNICODE);

    $paramsEncrypt = Encryption::Encrypt($paramsJsonEncode, $this->_API_KET_ENCRYPTION_DATA);
    return $paramsEncrypt;
  }

	public function buildHttpRequest(array $input, string $functionName = '', string $method="POST"): Request
	{
		$requestInput = $this->_makeRequestInput($input, $functionName);
		$dataPost = json_encode($requestInput, JSON_UNESCAPED_UNICODE);

		$headers = [
			'Content-Type' => 'application/json; charset=UTF-8',
			'Cache-Control' => 'no-cache',
		];

		return new Request($method, $this->_API_URL, $headers, $dataPost);
	}
} // End class