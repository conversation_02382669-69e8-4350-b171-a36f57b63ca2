<?php

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Route;

Route::any('/CheckRedis', function () {
	dump(Cache::get("ListTrichNo"));
});

Route::any('/SetRedis', function () {
	$r = Cache::put("ListTrichNo", "ok", now()->addMinutes(5));
	dump($r);

	if (!$r) {
		throw new Exception('Loi set redis');
	}
});

Route::any('/RemoveCache', function () {
	Cache::forget("ListTrichNo");
});
