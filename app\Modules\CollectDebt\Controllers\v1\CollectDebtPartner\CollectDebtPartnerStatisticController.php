<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtPartner;

use App\Lib\Helper;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Model\CollectDebtStatisticPartner;
use App\Modules\CollectDebt\Requests\v1\CollectDebtGuide\DebtRecoveryContractGuideGetPaymentStatisticRequest;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerStatisticByChannelAction\GetStatisticPartnerV2Action;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerStatisticByChannelAction\DebtRecoveryPartnerStatisticByChannelAction;

class CollectDebtPartnerStatisticController extends Controller
{
	
	public function statisticByChannel(DebtRecoveryContractGuideGetPaymentStatisticRequest $request)
	{
		try {
			$statistic = app(DebtRecoveryPartnerStatisticByChannelAction::class)->run($request);
			return $this->successResponse($statistic, $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	} 


	public function GetStatisticPartnerV2(DebtRecoveryContractGuideGetPaymentStatisticRequest $request)
	{
		try {
			$statistic = app(GetStatisticPartnerV2Action::class)->run($request);
			return $this->successResponse($statistic, $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	} 
} // End class
