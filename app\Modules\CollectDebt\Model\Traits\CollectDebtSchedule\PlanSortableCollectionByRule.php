<?php

namespace App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule;

use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtSetting;
use Illuminate\Database\Eloquent\Collection;

class PlanSortableCollectionByRule
{
  // $plans đã được groupBy theo date
  public function run(Collection $plans): Collection
  {
    $plans = $plans->transform(function (Collection $planCollection) {
      $planCollection = $this->sortCollection($planCollection);
      return $planCollection;
    }); 

    return $plans;
  }

  public function sortCollection(Collection $planCollection): Collection {
		// if (CollectDebtSetting::isEnableRuleCanTruGocTruocPhiSau()) {
		// 	return $this->__handleRuleCanTruGocTruocPhiSau($planCollection);
		// }

		return $this->__handleRuleCanTruCuonChieu($planCollection);
  }

  private function __handleRuleCanTruGocTruocPhiSau(Collection $planCollection): Collection {
    $listLichThuGoc = $planCollection->filter(function (CollectDebtSchedule $p) {
			return $p->isLichThuGoc();
		})->sortBy('cycle_number');

		$listLichThuPhi = $planCollection->filter(function (CollectDebtSchedule $p) {
			return $p->isLichThuPhi();
		})->sortBy('cycle_number');

		$collection = Collection::make();

		if ($listLichThuGoc->isNotEmpty()) {
			foreach ($listLichThuGoc as $p) {
				$collection->push($p);
			}
		}

		if ($listLichThuPhi->isNotEmpty()) {
			foreach ($listLichThuPhi as $p) {
				$collection->push($p);
			}
		}

		return $collection;
  }

	private function __handleRuleCanTruCuonChieu(Collection $planCollection): Collection {
    $hasLichTatToan = $planCollection->contains(function (CollectDebtSchedule $plan) {
      return $plan->isLichTatToan();
    }); 

    $isTonTaiLichThuGocNhungKhongPhaiLichTatToan = $planCollection->contains(function (CollectDebtSchedule $plan) {
      return $plan->isKhongPhaiLichTatToan() && $plan->isLichThuGoc();
    });

		$isLichTatToanLaLichTuongLai = false;
		
		if ($hasLichTatToan) {
			$isLichTatToanLaLichTuongLai = $planCollection->contains(function (CollectDebtSchedule $plan) {
				return $plan->isLichTatToan() && $plan->rundate > now()->format('Ymd');
			}); 
		}

    // Nếu có lịch tất toán thì ưu tiên: lịch thu gốc > lịch thu phí chậm kỳ > lịch thu phí quá hạn
    if ( $hasLichTatToan && !$isTonTaiLichThuGocNhungKhongPhaiLichTatToan ) {

			$listLichThuGoc = $planCollection->filter(function (CollectDebtSchedule $p) {
				return $p->isLichThuGoc();
			});

			$listLichThuPhiChamKy = $planCollection->filter(function (CollectDebtSchedule $p) {
				return $p->isLichThuPhiChamKy();
			})->sortBy('cycle_number');

			$listLichThuPhiQuaHan = $planCollection->filter(function (CollectDebtSchedule $p) {
				return $p->isLichThuPhiQuaHan();
			});
      
			if ($isLichTatToanLaLichTuongLai) {
				$planCollection = Collection::make([])->concat($listLichThuPhiChamKy)
																						  ->concat($listLichThuGoc)
																						  ->concat($listLichThuPhiQuaHan);
			}else {
				$planCollection = Collection::make([])->concat($listLichThuGoc)
																						  ->concat($listLichThuPhiChamKy)
																						  ->concat($listLichThuPhiQuaHan);
			}

			return $planCollection;
    } 

    // Nếu không có lịch tất toán thì ưu tiên cuốn chiếu, kỳ nào đi kỳ đó
    if ( !$hasLichTatToan || $isTonTaiLichThuGocNhungKhongPhaiLichTatToan ) {
			$isTonTaiLichThuQuaKhu = $planCollection->contains(function (CollectDebtSchedule $p) {
				return $p->rundate < now()->format('Ymd');
			});

			// co lich thu qua khu
			if ($isTonTaiLichThuQuaKhu) {
				$isLichThuGocChinh = $planCollection->filter(function (CollectDebtSchedule $plan) {
					return $plan->isLichThuGocChinh();
				});

				$isLichThuGocVet = $planCollection->filter(function (CollectDebtSchedule $plan) {
					return $plan->isLichThuGocVet();
				})->sortBy('cycle_number');

				$isLichThuPhi = $planCollection->filter(function (CollectDebtSchedule $plan) {
					return $plan->isLichThuPhi();
				})->sortBy('cycle_number');


				$planCollection = Collection::make([])->concat($isLichThuGocChinh)
																							->concat($isLichThuGocVet)
																							->concat($isLichThuPhi);
			}
			
			// khong co lich thu qua khu
			if (!$isTonTaiLichThuQuaKhu) {
				
				$lichThuGocChinhHienTai = $planCollection->filter(function (CollectDebtSchedule $plan) {
					return $plan->isLichThuGocChinh() && $plan->rundate <= now()->format('Ymd');
				});
	
				$lichThuGocVetTuTruoc = $planCollection->filter(function (CollectDebtSchedule $plan) {
					return $plan->isLichThuGocVet() && $plan->rundate <= now()->format('Ymd');
				})->sortBy('cycle_number');
	
				$lichThuPhiDaSinhTuTruoc = $planCollection->filter(function (CollectDebtSchedule $plan) {
					return $plan->isLichThuPhi() && $plan->rundate <= now()->format('Ymd');
				})->sortBy('cycle_number');
	
				
			

				$list = Collection::make([])->concat($lichThuGocChinhHienTai)
																		->concat($lichThuGocVetTuTruoc)
																		->concat($lichThuPhiDaSinhTuTruoc);

				$lichThuTuongLai = $planCollection->filter(function (CollectDebtSchedule $plan) {
					return $plan->rundate > now()->format('Ymd');
				})->sortBy('cycle_number')->groupBy('cycle_number');

				if ($lichThuTuongLai->isNotEmpty()) {
					$sortLichThuTuongLai = $lichThuTuongLai->map(function ($listLichTuongLai) {
						return $listLichTuongLai->sortBy('isfee');
					});

					foreach ($sortLichThuTuongLai as $listLichTheoCycle) {
						foreach ($listLichTheoCycle as $p) {
							$list->push($p);
						}
					}
				}

				$planCollection = $list;
			} // End check qua khu
    } 

    return $planCollection;
  }
} // End class
